function tp(e,t){for(var r=0;r<t.length;r++){const n=t[r];if(typeof n!="string"&&!Array.isArray(n)){for(const s in n)if(s!=="default"&&!(s in e)){const o=Object.getOwnPropertyDescriptor(n,s);o&&Object.defineProperty(e,s,o.get?o:{enumerable:!0,get:()=>n[s]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))n(s);new MutationObserver(s=>{for(const o of s)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&n(i)}).observe(document,{childList:!0,subtree:!0});function r(s){const o={};return s.integrity&&(o.integrity=s.integrity),s.referrerPolicy&&(o.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?o.credentials="include":s.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function n(s){if(s.ep)return;s.ep=!0;const o=r(s);fetch(s.href,o)}})();function rp(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var lc={exports:{}},yo={},ic={exports:{}},$={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Xn=Symbol.for("react.element"),np=Symbol.for("react.portal"),sp=Symbol.for("react.fragment"),op=Symbol.for("react.strict_mode"),lp=Symbol.for("react.profiler"),ip=Symbol.for("react.provider"),ap=Symbol.for("react.context"),up=Symbol.for("react.forward_ref"),cp=Symbol.for("react.suspense"),dp=Symbol.for("react.memo"),fp=Symbol.for("react.lazy"),Ea=Symbol.iterator;function pp(e){return e===null||typeof e!="object"?null:(e=Ea&&e[Ea]||e["@@iterator"],typeof e=="function"?e:null)}var ac={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},uc=Object.assign,cc={};function Zr(e,t,r){this.props=e,this.context=t,this.refs=cc,this.updater=r||ac}Zr.prototype.isReactComponent={};Zr.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Zr.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function dc(){}dc.prototype=Zr.prototype;function Si(e,t,r){this.props=e,this.context=t,this.refs=cc,this.updater=r||ac}var Ci=Si.prototype=new dc;Ci.constructor=Si;uc(Ci,Zr.prototype);Ci.isPureReactComponent=!0;var Pa=Array.isArray,fc=Object.prototype.hasOwnProperty,ki={current:null},pc={key:!0,ref:!0,__self:!0,__source:!0};function mc(e,t,r){var n,s={},o=null,i=null;if(t!=null)for(n in t.ref!==void 0&&(i=t.ref),t.key!==void 0&&(o=""+t.key),t)fc.call(t,n)&&!pc.hasOwnProperty(n)&&(s[n]=t[n]);var a=arguments.length-2;if(a===1)s.children=r;else if(1<a){for(var u=Array(a),c=0;c<a;c++)u[c]=arguments[c+2];s.children=u}if(e&&e.defaultProps)for(n in a=e.defaultProps,a)s[n]===void 0&&(s[n]=a[n]);return{$$typeof:Xn,type:e,key:o,ref:i,props:s,_owner:ki.current}}function mp(e,t){return{$$typeof:Xn,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function _i(e){return typeof e=="object"&&e!==null&&e.$$typeof===Xn}function hp(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(r){return t[r]})}var Ra=/\/+/g;function Vo(e,t){return typeof e=="object"&&e!==null&&e.key!=null?hp(""+e.key):t.toString(36)}function Cs(e,t,r,n,s){var o=typeof e;(o==="undefined"||o==="boolean")&&(e=null);var i=!1;if(e===null)i=!0;else switch(o){case"string":case"number":i=!0;break;case"object":switch(e.$$typeof){case Xn:case np:i=!0}}if(i)return i=e,s=s(i),e=n===""?"."+Vo(i,0):n,Pa(s)?(r="",e!=null&&(r=e.replace(Ra,"$&/")+"/"),Cs(s,t,r,"",function(c){return c})):s!=null&&(_i(s)&&(s=mp(s,r+(!s.key||i&&i.key===s.key?"":(""+s.key).replace(Ra,"$&/")+"/")+e)),t.push(s)),1;if(i=0,n=n===""?".":n+":",Pa(e))for(var a=0;a<e.length;a++){o=e[a];var u=n+Vo(o,a);i+=Cs(o,t,r,u,s)}else if(u=pp(e),typeof u=="function")for(e=u.call(e),a=0;!(o=e.next()).done;)o=o.value,u=n+Vo(o,a++),i+=Cs(o,t,r,u,s);else if(o==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return i}function is(e,t,r){if(e==null)return e;var n=[],s=0;return Cs(e,n,"","",function(o){return t.call(r,o,s++)}),n}function gp(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(r){(e._status===0||e._status===-1)&&(e._status=1,e._result=r)},function(r){(e._status===0||e._status===-1)&&(e._status=2,e._result=r)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Fe={current:null},ks={transition:null},xp={ReactCurrentDispatcher:Fe,ReactCurrentBatchConfig:ks,ReactCurrentOwner:ki};function hc(){throw Error("act(...) is not supported in production builds of React.")}$.Children={map:is,forEach:function(e,t,r){is(e,function(){t.apply(this,arguments)},r)},count:function(e){var t=0;return is(e,function(){t++}),t},toArray:function(e){return is(e,function(t){return t})||[]},only:function(e){if(!_i(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};$.Component=Zr;$.Fragment=sp;$.Profiler=lp;$.PureComponent=Si;$.StrictMode=op;$.Suspense=cp;$.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=xp;$.act=hc;$.cloneElement=function(e,t,r){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var n=uc({},e.props),s=e.key,o=e.ref,i=e._owner;if(t!=null){if(t.ref!==void 0&&(o=t.ref,i=ki.current),t.key!==void 0&&(s=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(u in t)fc.call(t,u)&&!pc.hasOwnProperty(u)&&(n[u]=t[u]===void 0&&a!==void 0?a[u]:t[u])}var u=arguments.length-2;if(u===1)n.children=r;else if(1<u){a=Array(u);for(var c=0;c<u;c++)a[c]=arguments[c+2];n.children=a}return{$$typeof:Xn,type:e.type,key:s,ref:o,props:n,_owner:i}};$.createContext=function(e){return e={$$typeof:ap,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:ip,_context:e},e.Consumer=e};$.createElement=mc;$.createFactory=function(e){var t=mc.bind(null,e);return t.type=e,t};$.createRef=function(){return{current:null}};$.forwardRef=function(e){return{$$typeof:up,render:e}};$.isValidElement=_i;$.lazy=function(e){return{$$typeof:fp,_payload:{_status:-1,_result:e},_init:gp}};$.memo=function(e,t){return{$$typeof:dp,type:e,compare:t===void 0?null:t}};$.startTransition=function(e){var t=ks.transition;ks.transition={};try{e()}finally{ks.transition=t}};$.unstable_act=hc;$.useCallback=function(e,t){return Fe.current.useCallback(e,t)};$.useContext=function(e){return Fe.current.useContext(e)};$.useDebugValue=function(){};$.useDeferredValue=function(e){return Fe.current.useDeferredValue(e)};$.useEffect=function(e,t){return Fe.current.useEffect(e,t)};$.useId=function(){return Fe.current.useId()};$.useImperativeHandle=function(e,t,r){return Fe.current.useImperativeHandle(e,t,r)};$.useInsertionEffect=function(e,t){return Fe.current.useInsertionEffect(e,t)};$.useLayoutEffect=function(e,t){return Fe.current.useLayoutEffect(e,t)};$.useMemo=function(e,t){return Fe.current.useMemo(e,t)};$.useReducer=function(e,t,r){return Fe.current.useReducer(e,t,r)};$.useRef=function(e){return Fe.current.useRef(e)};$.useState=function(e){return Fe.current.useState(e)};$.useSyncExternalStore=function(e,t,r){return Fe.current.useSyncExternalStore(e,t,r)};$.useTransition=function(){return Fe.current.useTransition()};$.version="18.3.1";ic.exports=$;var j=ic.exports;const yp=rp(j),vp=tp({__proto__:null,default:yp},[j]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var wp=j,Np=Symbol.for("react.element"),jp=Symbol.for("react.fragment"),bp=Object.prototype.hasOwnProperty,Sp=wp.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Cp={key:!0,ref:!0,__self:!0,__source:!0};function gc(e,t,r){var n,s={},o=null,i=null;r!==void 0&&(o=""+r),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(i=t.ref);for(n in t)bp.call(t,n)&&!Cp.hasOwnProperty(n)&&(s[n]=t[n]);if(e&&e.defaultProps)for(n in t=e.defaultProps,t)s[n]===void 0&&(s[n]=t[n]);return{$$typeof:Np,type:e,key:o,ref:i,props:s,_owner:Sp.current}}yo.Fragment=jp;yo.jsx=gc;yo.jsxs=gc;lc.exports=yo;var l=lc.exports,xc={exports:{}},Qe={},yc={exports:{}},vc={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(L,M){var A=L.length;L.push(M);e:for(;0<A;){var V=A-1>>>1,H=L[V];if(0<s(H,M))L[V]=M,L[A]=H,A=V;else break e}}function r(L){return L.length===0?null:L[0]}function n(L){if(L.length===0)return null;var M=L[0],A=L.pop();if(A!==M){L[0]=A;e:for(var V=0,H=L.length,he=H>>>1;V<he;){var ge=2*(V+1)-1,rt=L[ge],Te=ge+1,k=L[Te];if(0>s(rt,A))Te<H&&0>s(k,rt)?(L[V]=k,L[Te]=A,V=Te):(L[V]=rt,L[ge]=A,V=ge);else if(Te<H&&0>s(k,A))L[V]=k,L[Te]=A,V=Te;else break e}}return M}function s(L,M){var A=L.sortIndex-M.sortIndex;return A!==0?A:L.id-M.id}if(typeof performance=="object"&&typeof performance.now=="function"){var o=performance;e.unstable_now=function(){return o.now()}}else{var i=Date,a=i.now();e.unstable_now=function(){return i.now()-a}}var u=[],c=[],f=1,d=null,g=3,b=!1,x=!1,y=!1,S=typeof setTimeout=="function"?setTimeout:null,m=typeof clearTimeout=="function"?clearTimeout:null,p=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function h(L){for(var M=r(c);M!==null;){if(M.callback===null)n(c);else if(M.startTime<=L)n(c),M.sortIndex=M.expirationTime,t(u,M);else break;M=r(c)}}function N(L){if(y=!1,h(L),!x)if(r(u)!==null)x=!0,se(w);else{var M=r(c);M!==null&&oe(N,M.startTime-L)}}function w(L,M){x=!1,y&&(y=!1,m(R),R=-1),b=!0;var A=g;try{for(h(M),d=r(u);d!==null&&(!(d.expirationTime>M)||L&&!ae());){var V=d.callback;if(typeof V=="function"){d.callback=null,g=d.priorityLevel;var H=V(d.expirationTime<=M);M=e.unstable_now(),typeof H=="function"?d.callback=H:d===r(u)&&n(u),h(M)}else n(u);d=r(u)}if(d!==null)var he=!0;else{var ge=r(c);ge!==null&&oe(N,ge.startTime-M),he=!1}return he}finally{d=null,g=A,b=!1}}var _=!1,T=null,R=-1,D=5,F=-1;function ae(){return!(e.unstable_now()-F<D)}function Se(){if(T!==null){var L=e.unstable_now();F=L;var M=!0;try{M=T(!0,L)}finally{M?ve():(_=!1,T=null)}}else _=!1}var ve;if(typeof p=="function")ve=function(){p(Se)};else if(typeof MessageChannel<"u"){var X=new MessageChannel,we=X.port2;X.port1.onmessage=Se,ve=function(){we.postMessage(null)}}else ve=function(){S(Se,0)};function se(L){T=L,_||(_=!0,ve())}function oe(L,M){R=S(function(){L(e.unstable_now())},M)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(L){L.callback=null},e.unstable_continueExecution=function(){x||b||(x=!0,se(w))},e.unstable_forceFrameRate=function(L){0>L||125<L?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):D=0<L?Math.floor(1e3/L):5},e.unstable_getCurrentPriorityLevel=function(){return g},e.unstable_getFirstCallbackNode=function(){return r(u)},e.unstable_next=function(L){switch(g){case 1:case 2:case 3:var M=3;break;default:M=g}var A=g;g=M;try{return L()}finally{g=A}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(L,M){switch(L){case 1:case 2:case 3:case 4:case 5:break;default:L=3}var A=g;g=L;try{return M()}finally{g=A}},e.unstable_scheduleCallback=function(L,M,A){var V=e.unstable_now();switch(typeof A=="object"&&A!==null?(A=A.delay,A=typeof A=="number"&&0<A?V+A:V):A=V,L){case 1:var H=-1;break;case 2:H=250;break;case 5:H=**********;break;case 4:H=1e4;break;default:H=5e3}return H=A+H,L={id:f++,callback:M,priorityLevel:L,startTime:A,expirationTime:H,sortIndex:-1},A>V?(L.sortIndex=A,t(c,L),r(u)===null&&L===r(c)&&(y?(m(R),R=-1):y=!0,oe(N,A-V))):(L.sortIndex=H,t(u,L),x||b||(x=!0,se(w))),L},e.unstable_shouldYield=ae,e.unstable_wrapCallback=function(L){var M=g;return function(){var A=g;g=M;try{return L.apply(this,arguments)}finally{g=A}}}})(vc);yc.exports=vc;var kp=yc.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var _p=j,Ke=kp;function P(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,r=1;r<arguments.length;r++)t+="&args[]="+encodeURIComponent(arguments[r]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var wc=new Set,Ln={};function br(e,t){qr(e,t),qr(e+"Capture",t)}function qr(e,t){for(Ln[e]=t,e=0;e<t.length;e++)wc.add(t[e])}var Et=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),jl=Object.prototype.hasOwnProperty,Ep=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Ta={},La={};function Pp(e){return jl.call(La,e)?!0:jl.call(Ta,e)?!1:Ep.test(e)?La[e]=!0:(Ta[e]=!0,!1)}function Rp(e,t,r,n){if(r!==null&&r.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return n?!1:r!==null?!r.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Tp(e,t,r,n){if(t===null||typeof t>"u"||Rp(e,t,r,n))return!0;if(n)return!1;if(r!==null)switch(r.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Ae(e,t,r,n,s,o,i){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=n,this.attributeNamespace=s,this.mustUseProperty=r,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=i}var be={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){be[e]=new Ae(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];be[t]=new Ae(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){be[e]=new Ae(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){be[e]=new Ae(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){be[e]=new Ae(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){be[e]=new Ae(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){be[e]=new Ae(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){be[e]=new Ae(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){be[e]=new Ae(e,5,!1,e.toLowerCase(),null,!1,!1)});var Ei=/[\-:]([a-z])/g;function Pi(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Ei,Pi);be[t]=new Ae(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Ei,Pi);be[t]=new Ae(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Ei,Pi);be[t]=new Ae(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){be[e]=new Ae(e,1,!1,e.toLowerCase(),null,!1,!1)});be.xlinkHref=new Ae("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){be[e]=new Ae(e,1,!1,e.toLowerCase(),null,!0,!0)});function Ri(e,t,r,n){var s=be.hasOwnProperty(t)?be[t]:null;(s!==null?s.type!==0:n||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Tp(t,r,s,n)&&(r=null),n||s===null?Pp(t)&&(r===null?e.removeAttribute(t):e.setAttribute(t,""+r)):s.mustUseProperty?e[s.propertyName]=r===null?s.type===3?!1:"":r:(t=s.attributeName,n=s.attributeNamespace,r===null?e.removeAttribute(t):(s=s.type,r=s===3||s===4&&r===!0?"":""+r,n?e.setAttributeNS(n,t,r):e.setAttribute(t,r))))}var Lt=_p.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,as=Symbol.for("react.element"),_r=Symbol.for("react.portal"),Er=Symbol.for("react.fragment"),Ti=Symbol.for("react.strict_mode"),bl=Symbol.for("react.profiler"),Nc=Symbol.for("react.provider"),jc=Symbol.for("react.context"),Li=Symbol.for("react.forward_ref"),Sl=Symbol.for("react.suspense"),Cl=Symbol.for("react.suspense_list"),Oi=Symbol.for("react.memo"),Ut=Symbol.for("react.lazy"),bc=Symbol.for("react.offscreen"),Oa=Symbol.iterator;function un(e){return e===null||typeof e!="object"?null:(e=Oa&&e[Oa]||e["@@iterator"],typeof e=="function"?e:null)}var re=Object.assign,qo;function vn(e){if(qo===void 0)try{throw Error()}catch(r){var t=r.stack.trim().match(/\n( *(at )?)/);qo=t&&t[1]||""}return`
`+qo+e}var Ko=!1;function Qo(e,t){if(!e||Ko)return"";Ko=!0;var r=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var n=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){n=c}e.call(t.prototype)}else{try{throw Error()}catch(c){n=c}e()}}catch(c){if(c&&n&&typeof c.stack=="string"){for(var s=c.stack.split(`
`),o=n.stack.split(`
`),i=s.length-1,a=o.length-1;1<=i&&0<=a&&s[i]!==o[a];)a--;for(;1<=i&&0<=a;i--,a--)if(s[i]!==o[a]){if(i!==1||a!==1)do if(i--,a--,0>a||s[i]!==o[a]){var u=`
`+s[i].replace(" at new "," at ");return e.displayName&&u.includes("<anonymous>")&&(u=u.replace("<anonymous>",e.displayName)),u}while(1<=i&&0<=a);break}}}finally{Ko=!1,Error.prepareStackTrace=r}return(e=e?e.displayName||e.name:"")?vn(e):""}function Lp(e){switch(e.tag){case 5:return vn(e.type);case 16:return vn("Lazy");case 13:return vn("Suspense");case 19:return vn("SuspenseList");case 0:case 2:case 15:return e=Qo(e.type,!1),e;case 11:return e=Qo(e.type.render,!1),e;case 1:return e=Qo(e.type,!0),e;default:return""}}function kl(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Er:return"Fragment";case _r:return"Portal";case bl:return"Profiler";case Ti:return"StrictMode";case Sl:return"Suspense";case Cl:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case jc:return(e.displayName||"Context")+".Consumer";case Nc:return(e._context.displayName||"Context")+".Provider";case Li:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Oi:return t=e.displayName||null,t!==null?t:kl(e.type)||"Memo";case Ut:t=e._payload,e=e._init;try{return kl(e(t))}catch{}}return null}function Op(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return kl(t);case 8:return t===Ti?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Zt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Sc(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Mp(e){var t=Sc(e)?"checked":"value",r=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),n=""+e[t];if(!e.hasOwnProperty(t)&&typeof r<"u"&&typeof r.get=="function"&&typeof r.set=="function"){var s=r.get,o=r.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return s.call(this)},set:function(i){n=""+i,o.call(this,i)}}),Object.defineProperty(e,t,{enumerable:r.enumerable}),{getValue:function(){return n},setValue:function(i){n=""+i},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function us(e){e._valueTracker||(e._valueTracker=Mp(e))}function Cc(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var r=t.getValue(),n="";return e&&(n=Sc(e)?e.checked?"true":"false":e.value),e=n,e!==r?(t.setValue(e),!0):!1}function Bs(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function _l(e,t){var r=t.checked;return re({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:r??e._wrapperState.initialChecked})}function Ma(e,t){var r=t.defaultValue==null?"":t.defaultValue,n=t.checked!=null?t.checked:t.defaultChecked;r=Zt(t.value!=null?t.value:r),e._wrapperState={initialChecked:n,initialValue:r,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function kc(e,t){t=t.checked,t!=null&&Ri(e,"checked",t,!1)}function El(e,t){kc(e,t);var r=Zt(t.value),n=t.type;if(r!=null)n==="number"?(r===0&&e.value===""||e.value!=r)&&(e.value=""+r):e.value!==""+r&&(e.value=""+r);else if(n==="submit"||n==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Pl(e,t.type,r):t.hasOwnProperty("defaultValue")&&Pl(e,t.type,Zt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Fa(e,t,r){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var n=t.type;if(!(n!=="submit"&&n!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,r||t===e.value||(e.value=t),e.defaultValue=t}r=e.name,r!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,r!==""&&(e.name=r)}function Pl(e,t,r){(t!=="number"||Bs(e.ownerDocument)!==e)&&(r==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+r&&(e.defaultValue=""+r))}var wn=Array.isArray;function Ir(e,t,r,n){if(e=e.options,t){t={};for(var s=0;s<r.length;s++)t["$"+r[s]]=!0;for(r=0;r<e.length;r++)s=t.hasOwnProperty("$"+e[r].value),e[r].selected!==s&&(e[r].selected=s),s&&n&&(e[r].defaultSelected=!0)}else{for(r=""+Zt(r),t=null,s=0;s<e.length;s++){if(e[s].value===r){e[s].selected=!0,n&&(e[s].defaultSelected=!0);return}t!==null||e[s].disabled||(t=e[s])}t!==null&&(t.selected=!0)}}function Rl(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(P(91));return re({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Aa(e,t){var r=t.value;if(r==null){if(r=t.children,t=t.defaultValue,r!=null){if(t!=null)throw Error(P(92));if(wn(r)){if(1<r.length)throw Error(P(93));r=r[0]}t=r}t==null&&(t=""),r=t}e._wrapperState={initialValue:Zt(r)}}function _c(e,t){var r=Zt(t.value),n=Zt(t.defaultValue);r!=null&&(r=""+r,r!==e.value&&(e.value=r),t.defaultValue==null&&e.defaultValue!==r&&(e.defaultValue=r)),n!=null&&(e.defaultValue=""+n)}function Ua(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Ec(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Tl(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Ec(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var cs,Pc=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,r,n,s){MSApp.execUnsafeLocalFunction(function(){return e(t,r,n,s)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(cs=cs||document.createElement("div"),cs.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=cs.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function On(e,t){if(t){var r=e.firstChild;if(r&&r===e.lastChild&&r.nodeType===3){r.nodeValue=t;return}}e.textContent=t}var bn={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Fp=["Webkit","ms","Moz","O"];Object.keys(bn).forEach(function(e){Fp.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),bn[t]=bn[e]})});function Rc(e,t,r){return t==null||typeof t=="boolean"||t===""?"":r||typeof t!="number"||t===0||bn.hasOwnProperty(e)&&bn[e]?(""+t).trim():t+"px"}function Tc(e,t){e=e.style;for(var r in t)if(t.hasOwnProperty(r)){var n=r.indexOf("--")===0,s=Rc(r,t[r],n);r==="float"&&(r="cssFloat"),n?e.setProperty(r,s):e[r]=s}}var Ap=re({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Ll(e,t){if(t){if(Ap[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(P(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(P(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(P(61))}if(t.style!=null&&typeof t.style!="object")throw Error(P(62))}}function Ol(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Ml=null;function Mi(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Fl=null,$r=null,Br=null;function za(e){if(e=ts(e)){if(typeof Fl!="function")throw Error(P(280));var t=e.stateNode;t&&(t=bo(t),Fl(e.stateNode,e.type,t))}}function Lc(e){$r?Br?Br.push(e):Br=[e]:$r=e}function Oc(){if($r){var e=$r,t=Br;if(Br=$r=null,za(e),t)for(e=0;e<t.length;e++)za(t[e])}}function Mc(e,t){return e(t)}function Fc(){}var Jo=!1;function Ac(e,t,r){if(Jo)return e(t,r);Jo=!0;try{return Mc(e,t,r)}finally{Jo=!1,($r!==null||Br!==null)&&(Fc(),Oc())}}function Mn(e,t){var r=e.stateNode;if(r===null)return null;var n=bo(r);if(n===null)return null;r=n[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(n=!n.disabled)||(e=e.type,n=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!n;break e;default:e=!1}if(e)return null;if(r&&typeof r!="function")throw Error(P(231,t,typeof r));return r}var Al=!1;if(Et)try{var cn={};Object.defineProperty(cn,"passive",{get:function(){Al=!0}}),window.addEventListener("test",cn,cn),window.removeEventListener("test",cn,cn)}catch{Al=!1}function Up(e,t,r,n,s,o,i,a,u){var c=Array.prototype.slice.call(arguments,3);try{t.apply(r,c)}catch(f){this.onError(f)}}var Sn=!1,Hs=null,Ws=!1,Ul=null,zp={onError:function(e){Sn=!0,Hs=e}};function Dp(e,t,r,n,s,o,i,a,u){Sn=!1,Hs=null,Up.apply(zp,arguments)}function Ip(e,t,r,n,s,o,i,a,u){if(Dp.apply(this,arguments),Sn){if(Sn){var c=Hs;Sn=!1,Hs=null}else throw Error(P(198));Ws||(Ws=!0,Ul=c)}}function Sr(e){var t=e,r=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(r=t.return),e=t.return;while(e)}return t.tag===3?r:null}function Uc(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Da(e){if(Sr(e)!==e)throw Error(P(188))}function $p(e){var t=e.alternate;if(!t){if(t=Sr(e),t===null)throw Error(P(188));return t!==e?null:e}for(var r=e,n=t;;){var s=r.return;if(s===null)break;var o=s.alternate;if(o===null){if(n=s.return,n!==null){r=n;continue}break}if(s.child===o.child){for(o=s.child;o;){if(o===r)return Da(s),e;if(o===n)return Da(s),t;o=o.sibling}throw Error(P(188))}if(r.return!==n.return)r=s,n=o;else{for(var i=!1,a=s.child;a;){if(a===r){i=!0,r=s,n=o;break}if(a===n){i=!0,n=s,r=o;break}a=a.sibling}if(!i){for(a=o.child;a;){if(a===r){i=!0,r=o,n=s;break}if(a===n){i=!0,n=o,r=s;break}a=a.sibling}if(!i)throw Error(P(189))}}if(r.alternate!==n)throw Error(P(190))}if(r.tag!==3)throw Error(P(188));return r.stateNode.current===r?e:t}function zc(e){return e=$p(e),e!==null?Dc(e):null}function Dc(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Dc(e);if(t!==null)return t;e=e.sibling}return null}var Ic=Ke.unstable_scheduleCallback,Ia=Ke.unstable_cancelCallback,Bp=Ke.unstable_shouldYield,Hp=Ke.unstable_requestPaint,le=Ke.unstable_now,Wp=Ke.unstable_getCurrentPriorityLevel,Fi=Ke.unstable_ImmediatePriority,$c=Ke.unstable_UserBlockingPriority,Vs=Ke.unstable_NormalPriority,Vp=Ke.unstable_LowPriority,Bc=Ke.unstable_IdlePriority,vo=null,wt=null;function qp(e){if(wt&&typeof wt.onCommitFiberRoot=="function")try{wt.onCommitFiberRoot(vo,e,void 0,(e.current.flags&128)===128)}catch{}}var it=Math.clz32?Math.clz32:Jp,Kp=Math.log,Qp=Math.LN2;function Jp(e){return e>>>=0,e===0?32:31-(Kp(e)/Qp|0)|0}var ds=64,fs=4194304;function Nn(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function qs(e,t){var r=e.pendingLanes;if(r===0)return 0;var n=0,s=e.suspendedLanes,o=e.pingedLanes,i=r&268435455;if(i!==0){var a=i&~s;a!==0?n=Nn(a):(o&=i,o!==0&&(n=Nn(o)))}else i=r&~s,i!==0?n=Nn(i):o!==0&&(n=Nn(o));if(n===0)return 0;if(t!==0&&t!==n&&!(t&s)&&(s=n&-n,o=t&-t,s>=o||s===16&&(o&4194240)!==0))return t;if(n&4&&(n|=r&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=n;0<t;)r=31-it(t),s=1<<r,n|=e[r],t&=~s;return n}function Yp(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Gp(e,t){for(var r=e.suspendedLanes,n=e.pingedLanes,s=e.expirationTimes,o=e.pendingLanes;0<o;){var i=31-it(o),a=1<<i,u=s[i];u===-1?(!(a&r)||a&n)&&(s[i]=Yp(a,t)):u<=t&&(e.expiredLanes|=a),o&=~a}}function zl(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Hc(){var e=ds;return ds<<=1,!(ds&4194240)&&(ds=64),e}function Yo(e){for(var t=[],r=0;31>r;r++)t.push(e);return t}function Zn(e,t,r){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-it(t),e[t]=r}function Xp(e,t){var r=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var n=e.eventTimes;for(e=e.expirationTimes;0<r;){var s=31-it(r),o=1<<s;t[s]=0,n[s]=-1,e[s]=-1,r&=~o}}function Ai(e,t){var r=e.entangledLanes|=t;for(e=e.entanglements;r;){var n=31-it(r),s=1<<n;s&t|e[n]&t&&(e[n]|=t),r&=~s}}var q=0;function Wc(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Vc,Ui,qc,Kc,Qc,Dl=!1,ps=[],Wt=null,Vt=null,qt=null,Fn=new Map,An=new Map,Dt=[],Zp="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function $a(e,t){switch(e){case"focusin":case"focusout":Wt=null;break;case"dragenter":case"dragleave":Vt=null;break;case"mouseover":case"mouseout":qt=null;break;case"pointerover":case"pointerout":Fn.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":An.delete(t.pointerId)}}function dn(e,t,r,n,s,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:r,eventSystemFlags:n,nativeEvent:o,targetContainers:[s]},t!==null&&(t=ts(t),t!==null&&Ui(t)),e):(e.eventSystemFlags|=n,t=e.targetContainers,s!==null&&t.indexOf(s)===-1&&t.push(s),e)}function em(e,t,r,n,s){switch(t){case"focusin":return Wt=dn(Wt,e,t,r,n,s),!0;case"dragenter":return Vt=dn(Vt,e,t,r,n,s),!0;case"mouseover":return qt=dn(qt,e,t,r,n,s),!0;case"pointerover":var o=s.pointerId;return Fn.set(o,dn(Fn.get(o)||null,e,t,r,n,s)),!0;case"gotpointercapture":return o=s.pointerId,An.set(o,dn(An.get(o)||null,e,t,r,n,s)),!0}return!1}function Jc(e){var t=cr(e.target);if(t!==null){var r=Sr(t);if(r!==null){if(t=r.tag,t===13){if(t=Uc(r),t!==null){e.blockedOn=t,Qc(e.priority,function(){qc(r)});return}}else if(t===3&&r.stateNode.current.memoizedState.isDehydrated){e.blockedOn=r.tag===3?r.stateNode.containerInfo:null;return}}}e.blockedOn=null}function _s(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var r=Il(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(r===null){r=e.nativeEvent;var n=new r.constructor(r.type,r);Ml=n,r.target.dispatchEvent(n),Ml=null}else return t=ts(r),t!==null&&Ui(t),e.blockedOn=r,!1;t.shift()}return!0}function Ba(e,t,r){_s(e)&&r.delete(t)}function tm(){Dl=!1,Wt!==null&&_s(Wt)&&(Wt=null),Vt!==null&&_s(Vt)&&(Vt=null),qt!==null&&_s(qt)&&(qt=null),Fn.forEach(Ba),An.forEach(Ba)}function fn(e,t){e.blockedOn===t&&(e.blockedOn=null,Dl||(Dl=!0,Ke.unstable_scheduleCallback(Ke.unstable_NormalPriority,tm)))}function Un(e){function t(s){return fn(s,e)}if(0<ps.length){fn(ps[0],e);for(var r=1;r<ps.length;r++){var n=ps[r];n.blockedOn===e&&(n.blockedOn=null)}}for(Wt!==null&&fn(Wt,e),Vt!==null&&fn(Vt,e),qt!==null&&fn(qt,e),Fn.forEach(t),An.forEach(t),r=0;r<Dt.length;r++)n=Dt[r],n.blockedOn===e&&(n.blockedOn=null);for(;0<Dt.length&&(r=Dt[0],r.blockedOn===null);)Jc(r),r.blockedOn===null&&Dt.shift()}var Hr=Lt.ReactCurrentBatchConfig,Ks=!0;function rm(e,t,r,n){var s=q,o=Hr.transition;Hr.transition=null;try{q=1,zi(e,t,r,n)}finally{q=s,Hr.transition=o}}function nm(e,t,r,n){var s=q,o=Hr.transition;Hr.transition=null;try{q=4,zi(e,t,r,n)}finally{q=s,Hr.transition=o}}function zi(e,t,r,n){if(Ks){var s=Il(e,t,r,n);if(s===null)ll(e,t,n,Qs,r),$a(e,n);else if(em(s,e,t,r,n))n.stopPropagation();else if($a(e,n),t&4&&-1<Zp.indexOf(e)){for(;s!==null;){var o=ts(s);if(o!==null&&Vc(o),o=Il(e,t,r,n),o===null&&ll(e,t,n,Qs,r),o===s)break;s=o}s!==null&&n.stopPropagation()}else ll(e,t,n,null,r)}}var Qs=null;function Il(e,t,r,n){if(Qs=null,e=Mi(n),e=cr(e),e!==null)if(t=Sr(e),t===null)e=null;else if(r=t.tag,r===13){if(e=Uc(t),e!==null)return e;e=null}else if(r===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Qs=e,null}function Yc(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Wp()){case Fi:return 1;case $c:return 4;case Vs:case Vp:return 16;case Bc:return 536870912;default:return 16}default:return 16}}var $t=null,Di=null,Es=null;function Gc(){if(Es)return Es;var e,t=Di,r=t.length,n,s="value"in $t?$t.value:$t.textContent,o=s.length;for(e=0;e<r&&t[e]===s[e];e++);var i=r-e;for(n=1;n<=i&&t[r-n]===s[o-n];n++);return Es=s.slice(e,1<n?1-n:void 0)}function Ps(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function ms(){return!0}function Ha(){return!1}function Je(e){function t(r,n,s,o,i){this._reactName=r,this._targetInst=s,this.type=n,this.nativeEvent=o,this.target=i,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(r=e[a],this[a]=r?r(o):o[a]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?ms:Ha,this.isPropagationStopped=Ha,this}return re(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var r=this.nativeEvent;r&&(r.preventDefault?r.preventDefault():typeof r.returnValue!="unknown"&&(r.returnValue=!1),this.isDefaultPrevented=ms)},stopPropagation:function(){var r=this.nativeEvent;r&&(r.stopPropagation?r.stopPropagation():typeof r.cancelBubble!="unknown"&&(r.cancelBubble=!0),this.isPropagationStopped=ms)},persist:function(){},isPersistent:ms}),t}var en={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Ii=Je(en),es=re({},en,{view:0,detail:0}),sm=Je(es),Go,Xo,pn,wo=re({},es,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:$i,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==pn&&(pn&&e.type==="mousemove"?(Go=e.screenX-pn.screenX,Xo=e.screenY-pn.screenY):Xo=Go=0,pn=e),Go)},movementY:function(e){return"movementY"in e?e.movementY:Xo}}),Wa=Je(wo),om=re({},wo,{dataTransfer:0}),lm=Je(om),im=re({},es,{relatedTarget:0}),Zo=Je(im),am=re({},en,{animationName:0,elapsedTime:0,pseudoElement:0}),um=Je(am),cm=re({},en,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),dm=Je(cm),fm=re({},en,{data:0}),Va=Je(fm),pm={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},mm={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},hm={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function gm(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=hm[e])?!!t[e]:!1}function $i(){return gm}var xm=re({},es,{key:function(e){if(e.key){var t=pm[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Ps(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?mm[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:$i,charCode:function(e){return e.type==="keypress"?Ps(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Ps(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),ym=Je(xm),vm=re({},wo,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),qa=Je(vm),wm=re({},es,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:$i}),Nm=Je(wm),jm=re({},en,{propertyName:0,elapsedTime:0,pseudoElement:0}),bm=Je(jm),Sm=re({},wo,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Cm=Je(Sm),km=[9,13,27,32],Bi=Et&&"CompositionEvent"in window,Cn=null;Et&&"documentMode"in document&&(Cn=document.documentMode);var _m=Et&&"TextEvent"in window&&!Cn,Xc=Et&&(!Bi||Cn&&8<Cn&&11>=Cn),Ka=String.fromCharCode(32),Qa=!1;function Zc(e,t){switch(e){case"keyup":return km.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function ed(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Pr=!1;function Em(e,t){switch(e){case"compositionend":return ed(t);case"keypress":return t.which!==32?null:(Qa=!0,Ka);case"textInput":return e=t.data,e===Ka&&Qa?null:e;default:return null}}function Pm(e,t){if(Pr)return e==="compositionend"||!Bi&&Zc(e,t)?(e=Gc(),Es=Di=$t=null,Pr=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Xc&&t.locale!=="ko"?null:t.data;default:return null}}var Rm={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Ja(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Rm[e.type]:t==="textarea"}function td(e,t,r,n){Lc(n),t=Js(t,"onChange"),0<t.length&&(r=new Ii("onChange","change",null,r,n),e.push({event:r,listeners:t}))}var kn=null,zn=null;function Tm(e){fd(e,0)}function No(e){var t=Lr(e);if(Cc(t))return e}function Lm(e,t){if(e==="change")return t}var rd=!1;if(Et){var el;if(Et){var tl="oninput"in document;if(!tl){var Ya=document.createElement("div");Ya.setAttribute("oninput","return;"),tl=typeof Ya.oninput=="function"}el=tl}else el=!1;rd=el&&(!document.documentMode||9<document.documentMode)}function Ga(){kn&&(kn.detachEvent("onpropertychange",nd),zn=kn=null)}function nd(e){if(e.propertyName==="value"&&No(zn)){var t=[];td(t,zn,e,Mi(e)),Ac(Tm,t)}}function Om(e,t,r){e==="focusin"?(Ga(),kn=t,zn=r,kn.attachEvent("onpropertychange",nd)):e==="focusout"&&Ga()}function Mm(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return No(zn)}function Fm(e,t){if(e==="click")return No(t)}function Am(e,t){if(e==="input"||e==="change")return No(t)}function Um(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var dt=typeof Object.is=="function"?Object.is:Um;function Dn(e,t){if(dt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var r=Object.keys(e),n=Object.keys(t);if(r.length!==n.length)return!1;for(n=0;n<r.length;n++){var s=r[n];if(!jl.call(t,s)||!dt(e[s],t[s]))return!1}return!0}function Xa(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Za(e,t){var r=Xa(e);e=0;for(var n;r;){if(r.nodeType===3){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=Xa(r)}}function sd(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?sd(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function od(){for(var e=window,t=Bs();t instanceof e.HTMLIFrameElement;){try{var r=typeof t.contentWindow.location.href=="string"}catch{r=!1}if(r)e=t.contentWindow;else break;t=Bs(e.document)}return t}function Hi(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function zm(e){var t=od(),r=e.focusedElem,n=e.selectionRange;if(t!==r&&r&&r.ownerDocument&&sd(r.ownerDocument.documentElement,r)){if(n!==null&&Hi(r)){if(t=n.start,e=n.end,e===void 0&&(e=t),"selectionStart"in r)r.selectionStart=t,r.selectionEnd=Math.min(e,r.value.length);else if(e=(t=r.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var s=r.textContent.length,o=Math.min(n.start,s);n=n.end===void 0?o:Math.min(n.end,s),!e.extend&&o>n&&(s=n,n=o,o=s),s=Za(r,o);var i=Za(r,n);s&&i&&(e.rangeCount!==1||e.anchorNode!==s.node||e.anchorOffset!==s.offset||e.focusNode!==i.node||e.focusOffset!==i.offset)&&(t=t.createRange(),t.setStart(s.node,s.offset),e.removeAllRanges(),o>n?(e.addRange(t),e.extend(i.node,i.offset)):(t.setEnd(i.node,i.offset),e.addRange(t)))}}for(t=[],e=r;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof r.focus=="function"&&r.focus(),r=0;r<t.length;r++)e=t[r],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Dm=Et&&"documentMode"in document&&11>=document.documentMode,Rr=null,$l=null,_n=null,Bl=!1;function eu(e,t,r){var n=r.window===r?r.document:r.nodeType===9?r:r.ownerDocument;Bl||Rr==null||Rr!==Bs(n)||(n=Rr,"selectionStart"in n&&Hi(n)?n={start:n.selectionStart,end:n.selectionEnd}:(n=(n.ownerDocument&&n.ownerDocument.defaultView||window).getSelection(),n={anchorNode:n.anchorNode,anchorOffset:n.anchorOffset,focusNode:n.focusNode,focusOffset:n.focusOffset}),_n&&Dn(_n,n)||(_n=n,n=Js($l,"onSelect"),0<n.length&&(t=new Ii("onSelect","select",null,t,r),e.push({event:t,listeners:n}),t.target=Rr)))}function hs(e,t){var r={};return r[e.toLowerCase()]=t.toLowerCase(),r["Webkit"+e]="webkit"+t,r["Moz"+e]="moz"+t,r}var Tr={animationend:hs("Animation","AnimationEnd"),animationiteration:hs("Animation","AnimationIteration"),animationstart:hs("Animation","AnimationStart"),transitionend:hs("Transition","TransitionEnd")},rl={},ld={};Et&&(ld=document.createElement("div").style,"AnimationEvent"in window||(delete Tr.animationend.animation,delete Tr.animationiteration.animation,delete Tr.animationstart.animation),"TransitionEvent"in window||delete Tr.transitionend.transition);function jo(e){if(rl[e])return rl[e];if(!Tr[e])return e;var t=Tr[e],r;for(r in t)if(t.hasOwnProperty(r)&&r in ld)return rl[e]=t[r];return e}var id=jo("animationend"),ad=jo("animationiteration"),ud=jo("animationstart"),cd=jo("transitionend"),dd=new Map,tu="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function tr(e,t){dd.set(e,t),br(t,[e])}for(var nl=0;nl<tu.length;nl++){var sl=tu[nl],Im=sl.toLowerCase(),$m=sl[0].toUpperCase()+sl.slice(1);tr(Im,"on"+$m)}tr(id,"onAnimationEnd");tr(ad,"onAnimationIteration");tr(ud,"onAnimationStart");tr("dblclick","onDoubleClick");tr("focusin","onFocus");tr("focusout","onBlur");tr(cd,"onTransitionEnd");qr("onMouseEnter",["mouseout","mouseover"]);qr("onMouseLeave",["mouseout","mouseover"]);qr("onPointerEnter",["pointerout","pointerover"]);qr("onPointerLeave",["pointerout","pointerover"]);br("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));br("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));br("onBeforeInput",["compositionend","keypress","textInput","paste"]);br("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));br("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));br("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var jn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Bm=new Set("cancel close invalid load scroll toggle".split(" ").concat(jn));function ru(e,t,r){var n=e.type||"unknown-event";e.currentTarget=r,Ip(n,t,void 0,e),e.currentTarget=null}function fd(e,t){t=(t&4)!==0;for(var r=0;r<e.length;r++){var n=e[r],s=n.event;n=n.listeners;e:{var o=void 0;if(t)for(var i=n.length-1;0<=i;i--){var a=n[i],u=a.instance,c=a.currentTarget;if(a=a.listener,u!==o&&s.isPropagationStopped())break e;ru(s,a,c),o=u}else for(i=0;i<n.length;i++){if(a=n[i],u=a.instance,c=a.currentTarget,a=a.listener,u!==o&&s.isPropagationStopped())break e;ru(s,a,c),o=u}}}if(Ws)throw e=Ul,Ws=!1,Ul=null,e}function J(e,t){var r=t[Kl];r===void 0&&(r=t[Kl]=new Set);var n=e+"__bubble";r.has(n)||(pd(t,e,2,!1),r.add(n))}function ol(e,t,r){var n=0;t&&(n|=4),pd(r,e,n,t)}var gs="_reactListening"+Math.random().toString(36).slice(2);function In(e){if(!e[gs]){e[gs]=!0,wc.forEach(function(r){r!=="selectionchange"&&(Bm.has(r)||ol(r,!1,e),ol(r,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[gs]||(t[gs]=!0,ol("selectionchange",!1,t))}}function pd(e,t,r,n){switch(Yc(t)){case 1:var s=rm;break;case 4:s=nm;break;default:s=zi}r=s.bind(null,t,r,e),s=void 0,!Al||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(s=!0),n?s!==void 0?e.addEventListener(t,r,{capture:!0,passive:s}):e.addEventListener(t,r,!0):s!==void 0?e.addEventListener(t,r,{passive:s}):e.addEventListener(t,r,!1)}function ll(e,t,r,n,s){var o=n;if(!(t&1)&&!(t&2)&&n!==null)e:for(;;){if(n===null)return;var i=n.tag;if(i===3||i===4){var a=n.stateNode.containerInfo;if(a===s||a.nodeType===8&&a.parentNode===s)break;if(i===4)for(i=n.return;i!==null;){var u=i.tag;if((u===3||u===4)&&(u=i.stateNode.containerInfo,u===s||u.nodeType===8&&u.parentNode===s))return;i=i.return}for(;a!==null;){if(i=cr(a),i===null)return;if(u=i.tag,u===5||u===6){n=o=i;continue e}a=a.parentNode}}n=n.return}Ac(function(){var c=o,f=Mi(r),d=[];e:{var g=dd.get(e);if(g!==void 0){var b=Ii,x=e;switch(e){case"keypress":if(Ps(r)===0)break e;case"keydown":case"keyup":b=ym;break;case"focusin":x="focus",b=Zo;break;case"focusout":x="blur",b=Zo;break;case"beforeblur":case"afterblur":b=Zo;break;case"click":if(r.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":b=Wa;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":b=lm;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":b=Nm;break;case id:case ad:case ud:b=um;break;case cd:b=bm;break;case"scroll":b=sm;break;case"wheel":b=Cm;break;case"copy":case"cut":case"paste":b=dm;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":b=qa}var y=(t&4)!==0,S=!y&&e==="scroll",m=y?g!==null?g+"Capture":null:g;y=[];for(var p=c,h;p!==null;){h=p;var N=h.stateNode;if(h.tag===5&&N!==null&&(h=N,m!==null&&(N=Mn(p,m),N!=null&&y.push($n(p,N,h)))),S)break;p=p.return}0<y.length&&(g=new b(g,x,null,r,f),d.push({event:g,listeners:y}))}}if(!(t&7)){e:{if(g=e==="mouseover"||e==="pointerover",b=e==="mouseout"||e==="pointerout",g&&r!==Ml&&(x=r.relatedTarget||r.fromElement)&&(cr(x)||x[Pt]))break e;if((b||g)&&(g=f.window===f?f:(g=f.ownerDocument)?g.defaultView||g.parentWindow:window,b?(x=r.relatedTarget||r.toElement,b=c,x=x?cr(x):null,x!==null&&(S=Sr(x),x!==S||x.tag!==5&&x.tag!==6)&&(x=null)):(b=null,x=c),b!==x)){if(y=Wa,N="onMouseLeave",m="onMouseEnter",p="mouse",(e==="pointerout"||e==="pointerover")&&(y=qa,N="onPointerLeave",m="onPointerEnter",p="pointer"),S=b==null?g:Lr(b),h=x==null?g:Lr(x),g=new y(N,p+"leave",b,r,f),g.target=S,g.relatedTarget=h,N=null,cr(f)===c&&(y=new y(m,p+"enter",x,r,f),y.target=h,y.relatedTarget=S,N=y),S=N,b&&x)t:{for(y=b,m=x,p=0,h=y;h;h=kr(h))p++;for(h=0,N=m;N;N=kr(N))h++;for(;0<p-h;)y=kr(y),p--;for(;0<h-p;)m=kr(m),h--;for(;p--;){if(y===m||m!==null&&y===m.alternate)break t;y=kr(y),m=kr(m)}y=null}else y=null;b!==null&&nu(d,g,b,y,!1),x!==null&&S!==null&&nu(d,S,x,y,!0)}}e:{if(g=c?Lr(c):window,b=g.nodeName&&g.nodeName.toLowerCase(),b==="select"||b==="input"&&g.type==="file")var w=Lm;else if(Ja(g))if(rd)w=Am;else{w=Mm;var _=Om}else(b=g.nodeName)&&b.toLowerCase()==="input"&&(g.type==="checkbox"||g.type==="radio")&&(w=Fm);if(w&&(w=w(e,c))){td(d,w,r,f);break e}_&&_(e,g,c),e==="focusout"&&(_=g._wrapperState)&&_.controlled&&g.type==="number"&&Pl(g,"number",g.value)}switch(_=c?Lr(c):window,e){case"focusin":(Ja(_)||_.contentEditable==="true")&&(Rr=_,$l=c,_n=null);break;case"focusout":_n=$l=Rr=null;break;case"mousedown":Bl=!0;break;case"contextmenu":case"mouseup":case"dragend":Bl=!1,eu(d,r,f);break;case"selectionchange":if(Dm)break;case"keydown":case"keyup":eu(d,r,f)}var T;if(Bi)e:{switch(e){case"compositionstart":var R="onCompositionStart";break e;case"compositionend":R="onCompositionEnd";break e;case"compositionupdate":R="onCompositionUpdate";break e}R=void 0}else Pr?Zc(e,r)&&(R="onCompositionEnd"):e==="keydown"&&r.keyCode===229&&(R="onCompositionStart");R&&(Xc&&r.locale!=="ko"&&(Pr||R!=="onCompositionStart"?R==="onCompositionEnd"&&Pr&&(T=Gc()):($t=f,Di="value"in $t?$t.value:$t.textContent,Pr=!0)),_=Js(c,R),0<_.length&&(R=new Va(R,e,null,r,f),d.push({event:R,listeners:_}),T?R.data=T:(T=ed(r),T!==null&&(R.data=T)))),(T=_m?Em(e,r):Pm(e,r))&&(c=Js(c,"onBeforeInput"),0<c.length&&(f=new Va("onBeforeInput","beforeinput",null,r,f),d.push({event:f,listeners:c}),f.data=T))}fd(d,t)})}function $n(e,t,r){return{instance:e,listener:t,currentTarget:r}}function Js(e,t){for(var r=t+"Capture",n=[];e!==null;){var s=e,o=s.stateNode;s.tag===5&&o!==null&&(s=o,o=Mn(e,r),o!=null&&n.unshift($n(e,o,s)),o=Mn(e,t),o!=null&&n.push($n(e,o,s))),e=e.return}return n}function kr(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function nu(e,t,r,n,s){for(var o=t._reactName,i=[];r!==null&&r!==n;){var a=r,u=a.alternate,c=a.stateNode;if(u!==null&&u===n)break;a.tag===5&&c!==null&&(a=c,s?(u=Mn(r,o),u!=null&&i.unshift($n(r,u,a))):s||(u=Mn(r,o),u!=null&&i.push($n(r,u,a)))),r=r.return}i.length!==0&&e.push({event:t,listeners:i})}var Hm=/\r\n?/g,Wm=/\u0000|\uFFFD/g;function su(e){return(typeof e=="string"?e:""+e).replace(Hm,`
`).replace(Wm,"")}function xs(e,t,r){if(t=su(t),su(e)!==t&&r)throw Error(P(425))}function Ys(){}var Hl=null,Wl=null;function Vl(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var ql=typeof setTimeout=="function"?setTimeout:void 0,Vm=typeof clearTimeout=="function"?clearTimeout:void 0,ou=typeof Promise=="function"?Promise:void 0,qm=typeof queueMicrotask=="function"?queueMicrotask:typeof ou<"u"?function(e){return ou.resolve(null).then(e).catch(Km)}:ql;function Km(e){setTimeout(function(){throw e})}function il(e,t){var r=t,n=0;do{var s=r.nextSibling;if(e.removeChild(r),s&&s.nodeType===8)if(r=s.data,r==="/$"){if(n===0){e.removeChild(s),Un(t);return}n--}else r!=="$"&&r!=="$?"&&r!=="$!"||n++;r=s}while(r);Un(t)}function Kt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function lu(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var r=e.data;if(r==="$"||r==="$!"||r==="$?"){if(t===0)return e;t--}else r==="/$"&&t++}e=e.previousSibling}return null}var tn=Math.random().toString(36).slice(2),yt="__reactFiber$"+tn,Bn="__reactProps$"+tn,Pt="__reactContainer$"+tn,Kl="__reactEvents$"+tn,Qm="__reactListeners$"+tn,Jm="__reactHandles$"+tn;function cr(e){var t=e[yt];if(t)return t;for(var r=e.parentNode;r;){if(t=r[Pt]||r[yt]){if(r=t.alternate,t.child!==null||r!==null&&r.child!==null)for(e=lu(e);e!==null;){if(r=e[yt])return r;e=lu(e)}return t}e=r,r=e.parentNode}return null}function ts(e){return e=e[yt]||e[Pt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Lr(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(P(33))}function bo(e){return e[Bn]||null}var Ql=[],Or=-1;function rr(e){return{current:e}}function G(e){0>Or||(e.current=Ql[Or],Ql[Or]=null,Or--)}function Q(e,t){Or++,Ql[Or]=e.current,e.current=t}var er={},Re=rr(er),De=rr(!1),gr=er;function Kr(e,t){var r=e.type.contextTypes;if(!r)return er;var n=e.stateNode;if(n&&n.__reactInternalMemoizedUnmaskedChildContext===t)return n.__reactInternalMemoizedMaskedChildContext;var s={},o;for(o in r)s[o]=t[o];return n&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=s),s}function Ie(e){return e=e.childContextTypes,e!=null}function Gs(){G(De),G(Re)}function iu(e,t,r){if(Re.current!==er)throw Error(P(168));Q(Re,t),Q(De,r)}function md(e,t,r){var n=e.stateNode;if(t=t.childContextTypes,typeof n.getChildContext!="function")return r;n=n.getChildContext();for(var s in n)if(!(s in t))throw Error(P(108,Op(e)||"Unknown",s));return re({},r,n)}function Xs(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||er,gr=Re.current,Q(Re,e),Q(De,De.current),!0}function au(e,t,r){var n=e.stateNode;if(!n)throw Error(P(169));r?(e=md(e,t,gr),n.__reactInternalMemoizedMergedChildContext=e,G(De),G(Re),Q(Re,e)):G(De),Q(De,r)}var St=null,So=!1,al=!1;function hd(e){St===null?St=[e]:St.push(e)}function Ym(e){So=!0,hd(e)}function nr(){if(!al&&St!==null){al=!0;var e=0,t=q;try{var r=St;for(q=1;e<r.length;e++){var n=r[e];do n=n(!0);while(n!==null)}St=null,So=!1}catch(s){throw St!==null&&(St=St.slice(e+1)),Ic(Fi,nr),s}finally{q=t,al=!1}}return null}var Mr=[],Fr=0,Zs=null,eo=0,Ye=[],Ge=0,xr=null,Ct=1,kt="";function ar(e,t){Mr[Fr++]=eo,Mr[Fr++]=Zs,Zs=e,eo=t}function gd(e,t,r){Ye[Ge++]=Ct,Ye[Ge++]=kt,Ye[Ge++]=xr,xr=e;var n=Ct;e=kt;var s=32-it(n)-1;n&=~(1<<s),r+=1;var o=32-it(t)+s;if(30<o){var i=s-s%5;o=(n&(1<<i)-1).toString(32),n>>=i,s-=i,Ct=1<<32-it(t)+s|r<<s|n,kt=o+e}else Ct=1<<o|r<<s|n,kt=e}function Wi(e){e.return!==null&&(ar(e,1),gd(e,1,0))}function Vi(e){for(;e===Zs;)Zs=Mr[--Fr],Mr[Fr]=null,eo=Mr[--Fr],Mr[Fr]=null;for(;e===xr;)xr=Ye[--Ge],Ye[Ge]=null,kt=Ye[--Ge],Ye[Ge]=null,Ct=Ye[--Ge],Ye[Ge]=null}var qe=null,Ve=null,Z=!1,lt=null;function xd(e,t){var r=Xe(5,null,null,0);r.elementType="DELETED",r.stateNode=t,r.return=e,t=e.deletions,t===null?(e.deletions=[r],e.flags|=16):t.push(r)}function uu(e,t){switch(e.tag){case 5:var r=e.type;return t=t.nodeType!==1||r.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,qe=e,Ve=Kt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,qe=e,Ve=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(r=xr!==null?{id:Ct,overflow:kt}:null,e.memoizedState={dehydrated:t,treeContext:r,retryLane:1073741824},r=Xe(18,null,null,0),r.stateNode=t,r.return=e,e.child=r,qe=e,Ve=null,!0):!1;default:return!1}}function Jl(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Yl(e){if(Z){var t=Ve;if(t){var r=t;if(!uu(e,t)){if(Jl(e))throw Error(P(418));t=Kt(r.nextSibling);var n=qe;t&&uu(e,t)?xd(n,r):(e.flags=e.flags&-4097|2,Z=!1,qe=e)}}else{if(Jl(e))throw Error(P(418));e.flags=e.flags&-4097|2,Z=!1,qe=e}}}function cu(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;qe=e}function ys(e){if(e!==qe)return!1;if(!Z)return cu(e),Z=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Vl(e.type,e.memoizedProps)),t&&(t=Ve)){if(Jl(e))throw yd(),Error(P(418));for(;t;)xd(e,t),t=Kt(t.nextSibling)}if(cu(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(P(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var r=e.data;if(r==="/$"){if(t===0){Ve=Kt(e.nextSibling);break e}t--}else r!=="$"&&r!=="$!"&&r!=="$?"||t++}e=e.nextSibling}Ve=null}}else Ve=qe?Kt(e.stateNode.nextSibling):null;return!0}function yd(){for(var e=Ve;e;)e=Kt(e.nextSibling)}function Qr(){Ve=qe=null,Z=!1}function qi(e){lt===null?lt=[e]:lt.push(e)}var Gm=Lt.ReactCurrentBatchConfig;function mn(e,t,r){if(e=r.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(r._owner){if(r=r._owner,r){if(r.tag!==1)throw Error(P(309));var n=r.stateNode}if(!n)throw Error(P(147,e));var s=n,o=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===o?t.ref:(t=function(i){var a=s.refs;i===null?delete a[o]:a[o]=i},t._stringRef=o,t)}if(typeof e!="string")throw Error(P(284));if(!r._owner)throw Error(P(290,e))}return e}function vs(e,t){throw e=Object.prototype.toString.call(t),Error(P(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function du(e){var t=e._init;return t(e._payload)}function vd(e){function t(m,p){if(e){var h=m.deletions;h===null?(m.deletions=[p],m.flags|=16):h.push(p)}}function r(m,p){if(!e)return null;for(;p!==null;)t(m,p),p=p.sibling;return null}function n(m,p){for(m=new Map;p!==null;)p.key!==null?m.set(p.key,p):m.set(p.index,p),p=p.sibling;return m}function s(m,p){return m=Gt(m,p),m.index=0,m.sibling=null,m}function o(m,p,h){return m.index=h,e?(h=m.alternate,h!==null?(h=h.index,h<p?(m.flags|=2,p):h):(m.flags|=2,p)):(m.flags|=1048576,p)}function i(m){return e&&m.alternate===null&&(m.flags|=2),m}function a(m,p,h,N){return p===null||p.tag!==6?(p=hl(h,m.mode,N),p.return=m,p):(p=s(p,h),p.return=m,p)}function u(m,p,h,N){var w=h.type;return w===Er?f(m,p,h.props.children,N,h.key):p!==null&&(p.elementType===w||typeof w=="object"&&w!==null&&w.$$typeof===Ut&&du(w)===p.type)?(N=s(p,h.props),N.ref=mn(m,p,h),N.return=m,N):(N=As(h.type,h.key,h.props,null,m.mode,N),N.ref=mn(m,p,h),N.return=m,N)}function c(m,p,h,N){return p===null||p.tag!==4||p.stateNode.containerInfo!==h.containerInfo||p.stateNode.implementation!==h.implementation?(p=gl(h,m.mode,N),p.return=m,p):(p=s(p,h.children||[]),p.return=m,p)}function f(m,p,h,N,w){return p===null||p.tag!==7?(p=hr(h,m.mode,N,w),p.return=m,p):(p=s(p,h),p.return=m,p)}function d(m,p,h){if(typeof p=="string"&&p!==""||typeof p=="number")return p=hl(""+p,m.mode,h),p.return=m,p;if(typeof p=="object"&&p!==null){switch(p.$$typeof){case as:return h=As(p.type,p.key,p.props,null,m.mode,h),h.ref=mn(m,null,p),h.return=m,h;case _r:return p=gl(p,m.mode,h),p.return=m,p;case Ut:var N=p._init;return d(m,N(p._payload),h)}if(wn(p)||un(p))return p=hr(p,m.mode,h,null),p.return=m,p;vs(m,p)}return null}function g(m,p,h,N){var w=p!==null?p.key:null;if(typeof h=="string"&&h!==""||typeof h=="number")return w!==null?null:a(m,p,""+h,N);if(typeof h=="object"&&h!==null){switch(h.$$typeof){case as:return h.key===w?u(m,p,h,N):null;case _r:return h.key===w?c(m,p,h,N):null;case Ut:return w=h._init,g(m,p,w(h._payload),N)}if(wn(h)||un(h))return w!==null?null:f(m,p,h,N,null);vs(m,h)}return null}function b(m,p,h,N,w){if(typeof N=="string"&&N!==""||typeof N=="number")return m=m.get(h)||null,a(p,m,""+N,w);if(typeof N=="object"&&N!==null){switch(N.$$typeof){case as:return m=m.get(N.key===null?h:N.key)||null,u(p,m,N,w);case _r:return m=m.get(N.key===null?h:N.key)||null,c(p,m,N,w);case Ut:var _=N._init;return b(m,p,h,_(N._payload),w)}if(wn(N)||un(N))return m=m.get(h)||null,f(p,m,N,w,null);vs(p,N)}return null}function x(m,p,h,N){for(var w=null,_=null,T=p,R=p=0,D=null;T!==null&&R<h.length;R++){T.index>R?(D=T,T=null):D=T.sibling;var F=g(m,T,h[R],N);if(F===null){T===null&&(T=D);break}e&&T&&F.alternate===null&&t(m,T),p=o(F,p,R),_===null?w=F:_.sibling=F,_=F,T=D}if(R===h.length)return r(m,T),Z&&ar(m,R),w;if(T===null){for(;R<h.length;R++)T=d(m,h[R],N),T!==null&&(p=o(T,p,R),_===null?w=T:_.sibling=T,_=T);return Z&&ar(m,R),w}for(T=n(m,T);R<h.length;R++)D=b(T,m,R,h[R],N),D!==null&&(e&&D.alternate!==null&&T.delete(D.key===null?R:D.key),p=o(D,p,R),_===null?w=D:_.sibling=D,_=D);return e&&T.forEach(function(ae){return t(m,ae)}),Z&&ar(m,R),w}function y(m,p,h,N){var w=un(h);if(typeof w!="function")throw Error(P(150));if(h=w.call(h),h==null)throw Error(P(151));for(var _=w=null,T=p,R=p=0,D=null,F=h.next();T!==null&&!F.done;R++,F=h.next()){T.index>R?(D=T,T=null):D=T.sibling;var ae=g(m,T,F.value,N);if(ae===null){T===null&&(T=D);break}e&&T&&ae.alternate===null&&t(m,T),p=o(ae,p,R),_===null?w=ae:_.sibling=ae,_=ae,T=D}if(F.done)return r(m,T),Z&&ar(m,R),w;if(T===null){for(;!F.done;R++,F=h.next())F=d(m,F.value,N),F!==null&&(p=o(F,p,R),_===null?w=F:_.sibling=F,_=F);return Z&&ar(m,R),w}for(T=n(m,T);!F.done;R++,F=h.next())F=b(T,m,R,F.value,N),F!==null&&(e&&F.alternate!==null&&T.delete(F.key===null?R:F.key),p=o(F,p,R),_===null?w=F:_.sibling=F,_=F);return e&&T.forEach(function(Se){return t(m,Se)}),Z&&ar(m,R),w}function S(m,p,h,N){if(typeof h=="object"&&h!==null&&h.type===Er&&h.key===null&&(h=h.props.children),typeof h=="object"&&h!==null){switch(h.$$typeof){case as:e:{for(var w=h.key,_=p;_!==null;){if(_.key===w){if(w=h.type,w===Er){if(_.tag===7){r(m,_.sibling),p=s(_,h.props.children),p.return=m,m=p;break e}}else if(_.elementType===w||typeof w=="object"&&w!==null&&w.$$typeof===Ut&&du(w)===_.type){r(m,_.sibling),p=s(_,h.props),p.ref=mn(m,_,h),p.return=m,m=p;break e}r(m,_);break}else t(m,_);_=_.sibling}h.type===Er?(p=hr(h.props.children,m.mode,N,h.key),p.return=m,m=p):(N=As(h.type,h.key,h.props,null,m.mode,N),N.ref=mn(m,p,h),N.return=m,m=N)}return i(m);case _r:e:{for(_=h.key;p!==null;){if(p.key===_)if(p.tag===4&&p.stateNode.containerInfo===h.containerInfo&&p.stateNode.implementation===h.implementation){r(m,p.sibling),p=s(p,h.children||[]),p.return=m,m=p;break e}else{r(m,p);break}else t(m,p);p=p.sibling}p=gl(h,m.mode,N),p.return=m,m=p}return i(m);case Ut:return _=h._init,S(m,p,_(h._payload),N)}if(wn(h))return x(m,p,h,N);if(un(h))return y(m,p,h,N);vs(m,h)}return typeof h=="string"&&h!==""||typeof h=="number"?(h=""+h,p!==null&&p.tag===6?(r(m,p.sibling),p=s(p,h),p.return=m,m=p):(r(m,p),p=hl(h,m.mode,N),p.return=m,m=p),i(m)):r(m,p)}return S}var Jr=vd(!0),wd=vd(!1),to=rr(null),ro=null,Ar=null,Ki=null;function Qi(){Ki=Ar=ro=null}function Ji(e){var t=to.current;G(to),e._currentValue=t}function Gl(e,t,r){for(;e!==null;){var n=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,n!==null&&(n.childLanes|=t)):n!==null&&(n.childLanes&t)!==t&&(n.childLanes|=t),e===r)break;e=e.return}}function Wr(e,t){ro=e,Ki=Ar=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(ze=!0),e.firstContext=null)}function et(e){var t=e._currentValue;if(Ki!==e)if(e={context:e,memoizedValue:t,next:null},Ar===null){if(ro===null)throw Error(P(308));Ar=e,ro.dependencies={lanes:0,firstContext:e}}else Ar=Ar.next=e;return t}var dr=null;function Yi(e){dr===null?dr=[e]:dr.push(e)}function Nd(e,t,r,n){var s=t.interleaved;return s===null?(r.next=r,Yi(t)):(r.next=s.next,s.next=r),t.interleaved=r,Rt(e,n)}function Rt(e,t){e.lanes|=t;var r=e.alternate;for(r!==null&&(r.lanes|=t),r=e,e=e.return;e!==null;)e.childLanes|=t,r=e.alternate,r!==null&&(r.childLanes|=t),r=e,e=e.return;return r.tag===3?r.stateNode:null}var zt=!1;function Gi(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function jd(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function _t(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Qt(e,t,r){var n=e.updateQueue;if(n===null)return null;if(n=n.shared,W&2){var s=n.pending;return s===null?t.next=t:(t.next=s.next,s.next=t),n.pending=t,Rt(e,r)}return s=n.interleaved,s===null?(t.next=t,Yi(n)):(t.next=s.next,s.next=t),n.interleaved=t,Rt(e,r)}function Rs(e,t,r){if(t=t.updateQueue,t!==null&&(t=t.shared,(r&4194240)!==0)){var n=t.lanes;n&=e.pendingLanes,r|=n,t.lanes=r,Ai(e,r)}}function fu(e,t){var r=e.updateQueue,n=e.alternate;if(n!==null&&(n=n.updateQueue,r===n)){var s=null,o=null;if(r=r.firstBaseUpdate,r!==null){do{var i={eventTime:r.eventTime,lane:r.lane,tag:r.tag,payload:r.payload,callback:r.callback,next:null};o===null?s=o=i:o=o.next=i,r=r.next}while(r!==null);o===null?s=o=t:o=o.next=t}else s=o=t;r={baseState:n.baseState,firstBaseUpdate:s,lastBaseUpdate:o,shared:n.shared,effects:n.effects},e.updateQueue=r;return}e=r.lastBaseUpdate,e===null?r.firstBaseUpdate=t:e.next=t,r.lastBaseUpdate=t}function no(e,t,r,n){var s=e.updateQueue;zt=!1;var o=s.firstBaseUpdate,i=s.lastBaseUpdate,a=s.shared.pending;if(a!==null){s.shared.pending=null;var u=a,c=u.next;u.next=null,i===null?o=c:i.next=c,i=u;var f=e.alternate;f!==null&&(f=f.updateQueue,a=f.lastBaseUpdate,a!==i&&(a===null?f.firstBaseUpdate=c:a.next=c,f.lastBaseUpdate=u))}if(o!==null){var d=s.baseState;i=0,f=c=u=null,a=o;do{var g=a.lane,b=a.eventTime;if((n&g)===g){f!==null&&(f=f.next={eventTime:b,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var x=e,y=a;switch(g=t,b=r,y.tag){case 1:if(x=y.payload,typeof x=="function"){d=x.call(b,d,g);break e}d=x;break e;case 3:x.flags=x.flags&-65537|128;case 0:if(x=y.payload,g=typeof x=="function"?x.call(b,d,g):x,g==null)break e;d=re({},d,g);break e;case 2:zt=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,g=s.effects,g===null?s.effects=[a]:g.push(a))}else b={eventTime:b,lane:g,tag:a.tag,payload:a.payload,callback:a.callback,next:null},f===null?(c=f=b,u=d):f=f.next=b,i|=g;if(a=a.next,a===null){if(a=s.shared.pending,a===null)break;g=a,a=g.next,g.next=null,s.lastBaseUpdate=g,s.shared.pending=null}}while(1);if(f===null&&(u=d),s.baseState=u,s.firstBaseUpdate=c,s.lastBaseUpdate=f,t=s.shared.interleaved,t!==null){s=t;do i|=s.lane,s=s.next;while(s!==t)}else o===null&&(s.shared.lanes=0);vr|=i,e.lanes=i,e.memoizedState=d}}function pu(e,t,r){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var n=e[t],s=n.callback;if(s!==null){if(n.callback=null,n=r,typeof s!="function")throw Error(P(191,s));s.call(n)}}}var rs={},Nt=rr(rs),Hn=rr(rs),Wn=rr(rs);function fr(e){if(e===rs)throw Error(P(174));return e}function Xi(e,t){switch(Q(Wn,t),Q(Hn,e),Q(Nt,rs),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Tl(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Tl(t,e)}G(Nt),Q(Nt,t)}function Yr(){G(Nt),G(Hn),G(Wn)}function bd(e){fr(Wn.current);var t=fr(Nt.current),r=Tl(t,e.type);t!==r&&(Q(Hn,e),Q(Nt,r))}function Zi(e){Hn.current===e&&(G(Nt),G(Hn))}var ee=rr(0);function so(e){for(var t=e;t!==null;){if(t.tag===13){var r=t.memoizedState;if(r!==null&&(r=r.dehydrated,r===null||r.data==="$?"||r.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ul=[];function ea(){for(var e=0;e<ul.length;e++)ul[e]._workInProgressVersionPrimary=null;ul.length=0}var Ts=Lt.ReactCurrentDispatcher,cl=Lt.ReactCurrentBatchConfig,yr=0,te=null,pe=null,xe=null,oo=!1,En=!1,Vn=0,Xm=0;function Ce(){throw Error(P(321))}function ta(e,t){if(t===null)return!1;for(var r=0;r<t.length&&r<e.length;r++)if(!dt(e[r],t[r]))return!1;return!0}function ra(e,t,r,n,s,o){if(yr=o,te=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Ts.current=e===null||e.memoizedState===null?rh:nh,e=r(n,s),En){o=0;do{if(En=!1,Vn=0,25<=o)throw Error(P(301));o+=1,xe=pe=null,t.updateQueue=null,Ts.current=sh,e=r(n,s)}while(En)}if(Ts.current=lo,t=pe!==null&&pe.next!==null,yr=0,xe=pe=te=null,oo=!1,t)throw Error(P(300));return e}function na(){var e=Vn!==0;return Vn=0,e}function xt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return xe===null?te.memoizedState=xe=e:xe=xe.next=e,xe}function tt(){if(pe===null){var e=te.alternate;e=e!==null?e.memoizedState:null}else e=pe.next;var t=xe===null?te.memoizedState:xe.next;if(t!==null)xe=t,pe=e;else{if(e===null)throw Error(P(310));pe=e,e={memoizedState:pe.memoizedState,baseState:pe.baseState,baseQueue:pe.baseQueue,queue:pe.queue,next:null},xe===null?te.memoizedState=xe=e:xe=xe.next=e}return xe}function qn(e,t){return typeof t=="function"?t(e):t}function dl(e){var t=tt(),r=t.queue;if(r===null)throw Error(P(311));r.lastRenderedReducer=e;var n=pe,s=n.baseQueue,o=r.pending;if(o!==null){if(s!==null){var i=s.next;s.next=o.next,o.next=i}n.baseQueue=s=o,r.pending=null}if(s!==null){o=s.next,n=n.baseState;var a=i=null,u=null,c=o;do{var f=c.lane;if((yr&f)===f)u!==null&&(u=u.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),n=c.hasEagerState?c.eagerState:e(n,c.action);else{var d={lane:f,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};u===null?(a=u=d,i=n):u=u.next=d,te.lanes|=f,vr|=f}c=c.next}while(c!==null&&c!==o);u===null?i=n:u.next=a,dt(n,t.memoizedState)||(ze=!0),t.memoizedState=n,t.baseState=i,t.baseQueue=u,r.lastRenderedState=n}if(e=r.interleaved,e!==null){s=e;do o=s.lane,te.lanes|=o,vr|=o,s=s.next;while(s!==e)}else s===null&&(r.lanes=0);return[t.memoizedState,r.dispatch]}function fl(e){var t=tt(),r=t.queue;if(r===null)throw Error(P(311));r.lastRenderedReducer=e;var n=r.dispatch,s=r.pending,o=t.memoizedState;if(s!==null){r.pending=null;var i=s=s.next;do o=e(o,i.action),i=i.next;while(i!==s);dt(o,t.memoizedState)||(ze=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),r.lastRenderedState=o}return[o,n]}function Sd(){}function Cd(e,t){var r=te,n=tt(),s=t(),o=!dt(n.memoizedState,s);if(o&&(n.memoizedState=s,ze=!0),n=n.queue,sa(Ed.bind(null,r,n,e),[e]),n.getSnapshot!==t||o||xe!==null&&xe.memoizedState.tag&1){if(r.flags|=2048,Kn(9,_d.bind(null,r,n,s,t),void 0,null),ye===null)throw Error(P(349));yr&30||kd(r,t,s)}return s}function kd(e,t,r){e.flags|=16384,e={getSnapshot:t,value:r},t=te.updateQueue,t===null?(t={lastEffect:null,stores:null},te.updateQueue=t,t.stores=[e]):(r=t.stores,r===null?t.stores=[e]:r.push(e))}function _d(e,t,r,n){t.value=r,t.getSnapshot=n,Pd(t)&&Rd(e)}function Ed(e,t,r){return r(function(){Pd(t)&&Rd(e)})}function Pd(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!dt(e,r)}catch{return!0}}function Rd(e){var t=Rt(e,1);t!==null&&at(t,e,1,-1)}function mu(e){var t=xt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:qn,lastRenderedState:e},t.queue=e,e=e.dispatch=th.bind(null,te,e),[t.memoizedState,e]}function Kn(e,t,r,n){return e={tag:e,create:t,destroy:r,deps:n,next:null},t=te.updateQueue,t===null?(t={lastEffect:null,stores:null},te.updateQueue=t,t.lastEffect=e.next=e):(r=t.lastEffect,r===null?t.lastEffect=e.next=e:(n=r.next,r.next=e,e.next=n,t.lastEffect=e)),e}function Td(){return tt().memoizedState}function Ls(e,t,r,n){var s=xt();te.flags|=e,s.memoizedState=Kn(1|t,r,void 0,n===void 0?null:n)}function Co(e,t,r,n){var s=tt();n=n===void 0?null:n;var o=void 0;if(pe!==null){var i=pe.memoizedState;if(o=i.destroy,n!==null&&ta(n,i.deps)){s.memoizedState=Kn(t,r,o,n);return}}te.flags|=e,s.memoizedState=Kn(1|t,r,o,n)}function hu(e,t){return Ls(8390656,8,e,t)}function sa(e,t){return Co(2048,8,e,t)}function Ld(e,t){return Co(4,2,e,t)}function Od(e,t){return Co(4,4,e,t)}function Md(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Fd(e,t,r){return r=r!=null?r.concat([e]):null,Co(4,4,Md.bind(null,t,e),r)}function oa(){}function Ad(e,t){var r=tt();t=t===void 0?null:t;var n=r.memoizedState;return n!==null&&t!==null&&ta(t,n[1])?n[0]:(r.memoizedState=[e,t],e)}function Ud(e,t){var r=tt();t=t===void 0?null:t;var n=r.memoizedState;return n!==null&&t!==null&&ta(t,n[1])?n[0]:(e=e(),r.memoizedState=[e,t],e)}function zd(e,t,r){return yr&21?(dt(r,t)||(r=Hc(),te.lanes|=r,vr|=r,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,ze=!0),e.memoizedState=r)}function Zm(e,t){var r=q;q=r!==0&&4>r?r:4,e(!0);var n=cl.transition;cl.transition={};try{e(!1),t()}finally{q=r,cl.transition=n}}function Dd(){return tt().memoizedState}function eh(e,t,r){var n=Yt(e);if(r={lane:n,action:r,hasEagerState:!1,eagerState:null,next:null},Id(e))$d(t,r);else if(r=Nd(e,t,r,n),r!==null){var s=Me();at(r,e,n,s),Bd(r,t,n)}}function th(e,t,r){var n=Yt(e),s={lane:n,action:r,hasEagerState:!1,eagerState:null,next:null};if(Id(e))$d(t,s);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var i=t.lastRenderedState,a=o(i,r);if(s.hasEagerState=!0,s.eagerState=a,dt(a,i)){var u=t.interleaved;u===null?(s.next=s,Yi(t)):(s.next=u.next,u.next=s),t.interleaved=s;return}}catch{}finally{}r=Nd(e,t,s,n),r!==null&&(s=Me(),at(r,e,n,s),Bd(r,t,n))}}function Id(e){var t=e.alternate;return e===te||t!==null&&t===te}function $d(e,t){En=oo=!0;var r=e.pending;r===null?t.next=t:(t.next=r.next,r.next=t),e.pending=t}function Bd(e,t,r){if(r&4194240){var n=t.lanes;n&=e.pendingLanes,r|=n,t.lanes=r,Ai(e,r)}}var lo={readContext:et,useCallback:Ce,useContext:Ce,useEffect:Ce,useImperativeHandle:Ce,useInsertionEffect:Ce,useLayoutEffect:Ce,useMemo:Ce,useReducer:Ce,useRef:Ce,useState:Ce,useDebugValue:Ce,useDeferredValue:Ce,useTransition:Ce,useMutableSource:Ce,useSyncExternalStore:Ce,useId:Ce,unstable_isNewReconciler:!1},rh={readContext:et,useCallback:function(e,t){return xt().memoizedState=[e,t===void 0?null:t],e},useContext:et,useEffect:hu,useImperativeHandle:function(e,t,r){return r=r!=null?r.concat([e]):null,Ls(4194308,4,Md.bind(null,t,e),r)},useLayoutEffect:function(e,t){return Ls(4194308,4,e,t)},useInsertionEffect:function(e,t){return Ls(4,2,e,t)},useMemo:function(e,t){var r=xt();return t=t===void 0?null:t,e=e(),r.memoizedState=[e,t],e},useReducer:function(e,t,r){var n=xt();return t=r!==void 0?r(t):t,n.memoizedState=n.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},n.queue=e,e=e.dispatch=eh.bind(null,te,e),[n.memoizedState,e]},useRef:function(e){var t=xt();return e={current:e},t.memoizedState=e},useState:mu,useDebugValue:oa,useDeferredValue:function(e){return xt().memoizedState=e},useTransition:function(){var e=mu(!1),t=e[0];return e=Zm.bind(null,e[1]),xt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,r){var n=te,s=xt();if(Z){if(r===void 0)throw Error(P(407));r=r()}else{if(r=t(),ye===null)throw Error(P(349));yr&30||kd(n,t,r)}s.memoizedState=r;var o={value:r,getSnapshot:t};return s.queue=o,hu(Ed.bind(null,n,o,e),[e]),n.flags|=2048,Kn(9,_d.bind(null,n,o,r,t),void 0,null),r},useId:function(){var e=xt(),t=ye.identifierPrefix;if(Z){var r=kt,n=Ct;r=(n&~(1<<32-it(n)-1)).toString(32)+r,t=":"+t+"R"+r,r=Vn++,0<r&&(t+="H"+r.toString(32)),t+=":"}else r=Xm++,t=":"+t+"r"+r.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},nh={readContext:et,useCallback:Ad,useContext:et,useEffect:sa,useImperativeHandle:Fd,useInsertionEffect:Ld,useLayoutEffect:Od,useMemo:Ud,useReducer:dl,useRef:Td,useState:function(){return dl(qn)},useDebugValue:oa,useDeferredValue:function(e){var t=tt();return zd(t,pe.memoizedState,e)},useTransition:function(){var e=dl(qn)[0],t=tt().memoizedState;return[e,t]},useMutableSource:Sd,useSyncExternalStore:Cd,useId:Dd,unstable_isNewReconciler:!1},sh={readContext:et,useCallback:Ad,useContext:et,useEffect:sa,useImperativeHandle:Fd,useInsertionEffect:Ld,useLayoutEffect:Od,useMemo:Ud,useReducer:fl,useRef:Td,useState:function(){return fl(qn)},useDebugValue:oa,useDeferredValue:function(e){var t=tt();return pe===null?t.memoizedState=e:zd(t,pe.memoizedState,e)},useTransition:function(){var e=fl(qn)[0],t=tt().memoizedState;return[e,t]},useMutableSource:Sd,useSyncExternalStore:Cd,useId:Dd,unstable_isNewReconciler:!1};function st(e,t){if(e&&e.defaultProps){t=re({},t),e=e.defaultProps;for(var r in e)t[r]===void 0&&(t[r]=e[r]);return t}return t}function Xl(e,t,r,n){t=e.memoizedState,r=r(n,t),r=r==null?t:re({},t,r),e.memoizedState=r,e.lanes===0&&(e.updateQueue.baseState=r)}var ko={isMounted:function(e){return(e=e._reactInternals)?Sr(e)===e:!1},enqueueSetState:function(e,t,r){e=e._reactInternals;var n=Me(),s=Yt(e),o=_t(n,s);o.payload=t,r!=null&&(o.callback=r),t=Qt(e,o,s),t!==null&&(at(t,e,s,n),Rs(t,e,s))},enqueueReplaceState:function(e,t,r){e=e._reactInternals;var n=Me(),s=Yt(e),o=_t(n,s);o.tag=1,o.payload=t,r!=null&&(o.callback=r),t=Qt(e,o,s),t!==null&&(at(t,e,s,n),Rs(t,e,s))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var r=Me(),n=Yt(e),s=_t(r,n);s.tag=2,t!=null&&(s.callback=t),t=Qt(e,s,n),t!==null&&(at(t,e,n,r),Rs(t,e,n))}};function gu(e,t,r,n,s,o,i){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(n,o,i):t.prototype&&t.prototype.isPureReactComponent?!Dn(r,n)||!Dn(s,o):!0}function Hd(e,t,r){var n=!1,s=er,o=t.contextType;return typeof o=="object"&&o!==null?o=et(o):(s=Ie(t)?gr:Re.current,n=t.contextTypes,o=(n=n!=null)?Kr(e,s):er),t=new t(r,o),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=ko,e.stateNode=t,t._reactInternals=e,n&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=s,e.__reactInternalMemoizedMaskedChildContext=o),t}function xu(e,t,r,n){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(r,n),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(r,n),t.state!==e&&ko.enqueueReplaceState(t,t.state,null)}function Zl(e,t,r,n){var s=e.stateNode;s.props=r,s.state=e.memoizedState,s.refs={},Gi(e);var o=t.contextType;typeof o=="object"&&o!==null?s.context=et(o):(o=Ie(t)?gr:Re.current,s.context=Kr(e,o)),s.state=e.memoizedState,o=t.getDerivedStateFromProps,typeof o=="function"&&(Xl(e,t,o,r),s.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof s.getSnapshotBeforeUpdate=="function"||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(t=s.state,typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount(),t!==s.state&&ko.enqueueReplaceState(s,s.state,null),no(e,r,s,n),s.state=e.memoizedState),typeof s.componentDidMount=="function"&&(e.flags|=4194308)}function Gr(e,t){try{var r="",n=t;do r+=Lp(n),n=n.return;while(n);var s=r}catch(o){s=`
Error generating stack: `+o.message+`
`+o.stack}return{value:e,source:t,stack:s,digest:null}}function pl(e,t,r){return{value:e,source:null,stack:r??null,digest:t??null}}function ei(e,t){try{console.error(t.value)}catch(r){setTimeout(function(){throw r})}}var oh=typeof WeakMap=="function"?WeakMap:Map;function Wd(e,t,r){r=_t(-1,r),r.tag=3,r.payload={element:null};var n=t.value;return r.callback=function(){ao||(ao=!0,ci=n),ei(e,t)},r}function Vd(e,t,r){r=_t(-1,r),r.tag=3;var n=e.type.getDerivedStateFromError;if(typeof n=="function"){var s=t.value;r.payload=function(){return n(s)},r.callback=function(){ei(e,t)}}var o=e.stateNode;return o!==null&&typeof o.componentDidCatch=="function"&&(r.callback=function(){ei(e,t),typeof n!="function"&&(Jt===null?Jt=new Set([this]):Jt.add(this));var i=t.stack;this.componentDidCatch(t.value,{componentStack:i!==null?i:""})}),r}function yu(e,t,r){var n=e.pingCache;if(n===null){n=e.pingCache=new oh;var s=new Set;n.set(t,s)}else s=n.get(t),s===void 0&&(s=new Set,n.set(t,s));s.has(r)||(s.add(r),e=vh.bind(null,e,t,r),t.then(e,e))}function vu(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function wu(e,t,r,n,s){return e.mode&1?(e.flags|=65536,e.lanes=s,e):(e===t?e.flags|=65536:(e.flags|=128,r.flags|=131072,r.flags&=-52805,r.tag===1&&(r.alternate===null?r.tag=17:(t=_t(-1,1),t.tag=2,Qt(r,t,1))),r.lanes|=1),e)}var lh=Lt.ReactCurrentOwner,ze=!1;function Oe(e,t,r,n){t.child=e===null?wd(t,null,r,n):Jr(t,e.child,r,n)}function Nu(e,t,r,n,s){r=r.render;var o=t.ref;return Wr(t,s),n=ra(e,t,r,n,o,s),r=na(),e!==null&&!ze?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~s,Tt(e,t,s)):(Z&&r&&Wi(t),t.flags|=1,Oe(e,t,n,s),t.child)}function ju(e,t,r,n,s){if(e===null){var o=r.type;return typeof o=="function"&&!pa(o)&&o.defaultProps===void 0&&r.compare===null&&r.defaultProps===void 0?(t.tag=15,t.type=o,qd(e,t,o,n,s)):(e=As(r.type,null,n,t,t.mode,s),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,!(e.lanes&s)){var i=o.memoizedProps;if(r=r.compare,r=r!==null?r:Dn,r(i,n)&&e.ref===t.ref)return Tt(e,t,s)}return t.flags|=1,e=Gt(o,n),e.ref=t.ref,e.return=t,t.child=e}function qd(e,t,r,n,s){if(e!==null){var o=e.memoizedProps;if(Dn(o,n)&&e.ref===t.ref)if(ze=!1,t.pendingProps=n=o,(e.lanes&s)!==0)e.flags&131072&&(ze=!0);else return t.lanes=e.lanes,Tt(e,t,s)}return ti(e,t,r,n,s)}function Kd(e,t,r){var n=t.pendingProps,s=n.children,o=e!==null?e.memoizedState:null;if(n.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Q(zr,We),We|=r;else{if(!(r&1073741824))return e=o!==null?o.baseLanes|r:r,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Q(zr,We),We|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},n=o!==null?o.baseLanes:r,Q(zr,We),We|=n}else o!==null?(n=o.baseLanes|r,t.memoizedState=null):n=r,Q(zr,We),We|=n;return Oe(e,t,s,r),t.child}function Qd(e,t){var r=t.ref;(e===null&&r!==null||e!==null&&e.ref!==r)&&(t.flags|=512,t.flags|=2097152)}function ti(e,t,r,n,s){var o=Ie(r)?gr:Re.current;return o=Kr(t,o),Wr(t,s),r=ra(e,t,r,n,o,s),n=na(),e!==null&&!ze?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~s,Tt(e,t,s)):(Z&&n&&Wi(t),t.flags|=1,Oe(e,t,r,s),t.child)}function bu(e,t,r,n,s){if(Ie(r)){var o=!0;Xs(t)}else o=!1;if(Wr(t,s),t.stateNode===null)Os(e,t),Hd(t,r,n),Zl(t,r,n,s),n=!0;else if(e===null){var i=t.stateNode,a=t.memoizedProps;i.props=a;var u=i.context,c=r.contextType;typeof c=="object"&&c!==null?c=et(c):(c=Ie(r)?gr:Re.current,c=Kr(t,c));var f=r.getDerivedStateFromProps,d=typeof f=="function"||typeof i.getSnapshotBeforeUpdate=="function";d||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(a!==n||u!==c)&&xu(t,i,n,c),zt=!1;var g=t.memoizedState;i.state=g,no(t,n,i,s),u=t.memoizedState,a!==n||g!==u||De.current||zt?(typeof f=="function"&&(Xl(t,r,f,n),u=t.memoizedState),(a=zt||gu(t,r,a,n,g,u,c))?(d||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount()),typeof i.componentDidMount=="function"&&(t.flags|=4194308)):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=n,t.memoizedState=u),i.props=n,i.state=u,i.context=c,n=a):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),n=!1)}else{i=t.stateNode,jd(e,t),a=t.memoizedProps,c=t.type===t.elementType?a:st(t.type,a),i.props=c,d=t.pendingProps,g=i.context,u=r.contextType,typeof u=="object"&&u!==null?u=et(u):(u=Ie(r)?gr:Re.current,u=Kr(t,u));var b=r.getDerivedStateFromProps;(f=typeof b=="function"||typeof i.getSnapshotBeforeUpdate=="function")||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(a!==d||g!==u)&&xu(t,i,n,u),zt=!1,g=t.memoizedState,i.state=g,no(t,n,i,s);var x=t.memoizedState;a!==d||g!==x||De.current||zt?(typeof b=="function"&&(Xl(t,r,b,n),x=t.memoizedState),(c=zt||gu(t,r,c,n,g,x,u)||!1)?(f||typeof i.UNSAFE_componentWillUpdate!="function"&&typeof i.componentWillUpdate!="function"||(typeof i.componentWillUpdate=="function"&&i.componentWillUpdate(n,x,u),typeof i.UNSAFE_componentWillUpdate=="function"&&i.UNSAFE_componentWillUpdate(n,x,u)),typeof i.componentDidUpdate=="function"&&(t.flags|=4),typeof i.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof i.componentDidUpdate!="function"||a===e.memoizedProps&&g===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&g===e.memoizedState||(t.flags|=1024),t.memoizedProps=n,t.memoizedState=x),i.props=n,i.state=x,i.context=u,n=c):(typeof i.componentDidUpdate!="function"||a===e.memoizedProps&&g===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&g===e.memoizedState||(t.flags|=1024),n=!1)}return ri(e,t,r,n,o,s)}function ri(e,t,r,n,s,o){Qd(e,t);var i=(t.flags&128)!==0;if(!n&&!i)return s&&au(t,r,!1),Tt(e,t,o);n=t.stateNode,lh.current=t;var a=i&&typeof r.getDerivedStateFromError!="function"?null:n.render();return t.flags|=1,e!==null&&i?(t.child=Jr(t,e.child,null,o),t.child=Jr(t,null,a,o)):Oe(e,t,a,o),t.memoizedState=n.state,s&&au(t,r,!0),t.child}function Jd(e){var t=e.stateNode;t.pendingContext?iu(e,t.pendingContext,t.pendingContext!==t.context):t.context&&iu(e,t.context,!1),Xi(e,t.containerInfo)}function Su(e,t,r,n,s){return Qr(),qi(s),t.flags|=256,Oe(e,t,r,n),t.child}var ni={dehydrated:null,treeContext:null,retryLane:0};function si(e){return{baseLanes:e,cachePool:null,transitions:null}}function Yd(e,t,r){var n=t.pendingProps,s=ee.current,o=!1,i=(t.flags&128)!==0,a;if((a=i)||(a=e!==null&&e.memoizedState===null?!1:(s&2)!==0),a?(o=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(s|=1),Q(ee,s&1),e===null)return Yl(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(i=n.children,e=n.fallback,o?(n=t.mode,o=t.child,i={mode:"hidden",children:i},!(n&1)&&o!==null?(o.childLanes=0,o.pendingProps=i):o=Po(i,n,0,null),e=hr(e,n,r,null),o.return=t,e.return=t,o.sibling=e,t.child=o,t.child.memoizedState=si(r),t.memoizedState=ni,e):la(t,i));if(s=e.memoizedState,s!==null&&(a=s.dehydrated,a!==null))return ih(e,t,i,n,a,s,r);if(o){o=n.fallback,i=t.mode,s=e.child,a=s.sibling;var u={mode:"hidden",children:n.children};return!(i&1)&&t.child!==s?(n=t.child,n.childLanes=0,n.pendingProps=u,t.deletions=null):(n=Gt(s,u),n.subtreeFlags=s.subtreeFlags&14680064),a!==null?o=Gt(a,o):(o=hr(o,i,r,null),o.flags|=2),o.return=t,n.return=t,n.sibling=o,t.child=n,n=o,o=t.child,i=e.child.memoizedState,i=i===null?si(r):{baseLanes:i.baseLanes|r,cachePool:null,transitions:i.transitions},o.memoizedState=i,o.childLanes=e.childLanes&~r,t.memoizedState=ni,n}return o=e.child,e=o.sibling,n=Gt(o,{mode:"visible",children:n.children}),!(t.mode&1)&&(n.lanes=r),n.return=t,n.sibling=null,e!==null&&(r=t.deletions,r===null?(t.deletions=[e],t.flags|=16):r.push(e)),t.child=n,t.memoizedState=null,n}function la(e,t){return t=Po({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function ws(e,t,r,n){return n!==null&&qi(n),Jr(t,e.child,null,r),e=la(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function ih(e,t,r,n,s,o,i){if(r)return t.flags&256?(t.flags&=-257,n=pl(Error(P(422))),ws(e,t,i,n)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(o=n.fallback,s=t.mode,n=Po({mode:"visible",children:n.children},s,0,null),o=hr(o,s,i,null),o.flags|=2,n.return=t,o.return=t,n.sibling=o,t.child=n,t.mode&1&&Jr(t,e.child,null,i),t.child.memoizedState=si(i),t.memoizedState=ni,o);if(!(t.mode&1))return ws(e,t,i,null);if(s.data==="$!"){if(n=s.nextSibling&&s.nextSibling.dataset,n)var a=n.dgst;return n=a,o=Error(P(419)),n=pl(o,n,void 0),ws(e,t,i,n)}if(a=(i&e.childLanes)!==0,ze||a){if(n=ye,n!==null){switch(i&-i){case 4:s=2;break;case 16:s=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:s=32;break;case 536870912:s=268435456;break;default:s=0}s=s&(n.suspendedLanes|i)?0:s,s!==0&&s!==o.retryLane&&(o.retryLane=s,Rt(e,s),at(n,e,s,-1))}return fa(),n=pl(Error(P(421))),ws(e,t,i,n)}return s.data==="$?"?(t.flags|=128,t.child=e.child,t=wh.bind(null,e),s._reactRetry=t,null):(e=o.treeContext,Ve=Kt(s.nextSibling),qe=t,Z=!0,lt=null,e!==null&&(Ye[Ge++]=Ct,Ye[Ge++]=kt,Ye[Ge++]=xr,Ct=e.id,kt=e.overflow,xr=t),t=la(t,n.children),t.flags|=4096,t)}function Cu(e,t,r){e.lanes|=t;var n=e.alternate;n!==null&&(n.lanes|=t),Gl(e.return,t,r)}function ml(e,t,r,n,s){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:n,tail:r,tailMode:s}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=n,o.tail=r,o.tailMode=s)}function Gd(e,t,r){var n=t.pendingProps,s=n.revealOrder,o=n.tail;if(Oe(e,t,n.children,r),n=ee.current,n&2)n=n&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Cu(e,r,t);else if(e.tag===19)Cu(e,r,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}n&=1}if(Q(ee,n),!(t.mode&1))t.memoizedState=null;else switch(s){case"forwards":for(r=t.child,s=null;r!==null;)e=r.alternate,e!==null&&so(e)===null&&(s=r),r=r.sibling;r=s,r===null?(s=t.child,t.child=null):(s=r.sibling,r.sibling=null),ml(t,!1,s,r,o);break;case"backwards":for(r=null,s=t.child,t.child=null;s!==null;){if(e=s.alternate,e!==null&&so(e)===null){t.child=s;break}e=s.sibling,s.sibling=r,r=s,s=e}ml(t,!0,r,null,o);break;case"together":ml(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Os(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Tt(e,t,r){if(e!==null&&(t.dependencies=e.dependencies),vr|=t.lanes,!(r&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(P(153));if(t.child!==null){for(e=t.child,r=Gt(e,e.pendingProps),t.child=r,r.return=t;e.sibling!==null;)e=e.sibling,r=r.sibling=Gt(e,e.pendingProps),r.return=t;r.sibling=null}return t.child}function ah(e,t,r){switch(t.tag){case 3:Jd(t),Qr();break;case 5:bd(t);break;case 1:Ie(t.type)&&Xs(t);break;case 4:Xi(t,t.stateNode.containerInfo);break;case 10:var n=t.type._context,s=t.memoizedProps.value;Q(to,n._currentValue),n._currentValue=s;break;case 13:if(n=t.memoizedState,n!==null)return n.dehydrated!==null?(Q(ee,ee.current&1),t.flags|=128,null):r&t.child.childLanes?Yd(e,t,r):(Q(ee,ee.current&1),e=Tt(e,t,r),e!==null?e.sibling:null);Q(ee,ee.current&1);break;case 19:if(n=(r&t.childLanes)!==0,e.flags&128){if(n)return Gd(e,t,r);t.flags|=128}if(s=t.memoizedState,s!==null&&(s.rendering=null,s.tail=null,s.lastEffect=null),Q(ee,ee.current),n)break;return null;case 22:case 23:return t.lanes=0,Kd(e,t,r)}return Tt(e,t,r)}var Xd,oi,Zd,ef;Xd=function(e,t){for(var r=t.child;r!==null;){if(r.tag===5||r.tag===6)e.appendChild(r.stateNode);else if(r.tag!==4&&r.child!==null){r.child.return=r,r=r.child;continue}if(r===t)break;for(;r.sibling===null;){if(r.return===null||r.return===t)return;r=r.return}r.sibling.return=r.return,r=r.sibling}};oi=function(){};Zd=function(e,t,r,n){var s=e.memoizedProps;if(s!==n){e=t.stateNode,fr(Nt.current);var o=null;switch(r){case"input":s=_l(e,s),n=_l(e,n),o=[];break;case"select":s=re({},s,{value:void 0}),n=re({},n,{value:void 0}),o=[];break;case"textarea":s=Rl(e,s),n=Rl(e,n),o=[];break;default:typeof s.onClick!="function"&&typeof n.onClick=="function"&&(e.onclick=Ys)}Ll(r,n);var i;r=null;for(c in s)if(!n.hasOwnProperty(c)&&s.hasOwnProperty(c)&&s[c]!=null)if(c==="style"){var a=s[c];for(i in a)a.hasOwnProperty(i)&&(r||(r={}),r[i]="")}else c!=="dangerouslySetInnerHTML"&&c!=="children"&&c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&c!=="autoFocus"&&(Ln.hasOwnProperty(c)?o||(o=[]):(o=o||[]).push(c,null));for(c in n){var u=n[c];if(a=s!=null?s[c]:void 0,n.hasOwnProperty(c)&&u!==a&&(u!=null||a!=null))if(c==="style")if(a){for(i in a)!a.hasOwnProperty(i)||u&&u.hasOwnProperty(i)||(r||(r={}),r[i]="");for(i in u)u.hasOwnProperty(i)&&a[i]!==u[i]&&(r||(r={}),r[i]=u[i])}else r||(o||(o=[]),o.push(c,r)),r=u;else c==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,a=a?a.__html:void 0,u!=null&&a!==u&&(o=o||[]).push(c,u)):c==="children"?typeof u!="string"&&typeof u!="number"||(o=o||[]).push(c,""+u):c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&(Ln.hasOwnProperty(c)?(u!=null&&c==="onScroll"&&J("scroll",e),o||a===u||(o=[])):(o=o||[]).push(c,u))}r&&(o=o||[]).push("style",r);var c=o;(t.updateQueue=c)&&(t.flags|=4)}};ef=function(e,t,r,n){r!==n&&(t.flags|=4)};function hn(e,t){if(!Z)switch(e.tailMode){case"hidden":t=e.tail;for(var r=null;t!==null;)t.alternate!==null&&(r=t),t=t.sibling;r===null?e.tail=null:r.sibling=null;break;case"collapsed":r=e.tail;for(var n=null;r!==null;)r.alternate!==null&&(n=r),r=r.sibling;n===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:n.sibling=null}}function ke(e){var t=e.alternate!==null&&e.alternate.child===e.child,r=0,n=0;if(t)for(var s=e.child;s!==null;)r|=s.lanes|s.childLanes,n|=s.subtreeFlags&14680064,n|=s.flags&14680064,s.return=e,s=s.sibling;else for(s=e.child;s!==null;)r|=s.lanes|s.childLanes,n|=s.subtreeFlags,n|=s.flags,s.return=e,s=s.sibling;return e.subtreeFlags|=n,e.childLanes=r,t}function uh(e,t,r){var n=t.pendingProps;switch(Vi(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ke(t),null;case 1:return Ie(t.type)&&Gs(),ke(t),null;case 3:return n=t.stateNode,Yr(),G(De),G(Re),ea(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(e===null||e.child===null)&&(ys(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,lt!==null&&(pi(lt),lt=null))),oi(e,t),ke(t),null;case 5:Zi(t);var s=fr(Wn.current);if(r=t.type,e!==null&&t.stateNode!=null)Zd(e,t,r,n,s),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!n){if(t.stateNode===null)throw Error(P(166));return ke(t),null}if(e=fr(Nt.current),ys(t)){n=t.stateNode,r=t.type;var o=t.memoizedProps;switch(n[yt]=t,n[Bn]=o,e=(t.mode&1)!==0,r){case"dialog":J("cancel",n),J("close",n);break;case"iframe":case"object":case"embed":J("load",n);break;case"video":case"audio":for(s=0;s<jn.length;s++)J(jn[s],n);break;case"source":J("error",n);break;case"img":case"image":case"link":J("error",n),J("load",n);break;case"details":J("toggle",n);break;case"input":Ma(n,o),J("invalid",n);break;case"select":n._wrapperState={wasMultiple:!!o.multiple},J("invalid",n);break;case"textarea":Aa(n,o),J("invalid",n)}Ll(r,o),s=null;for(var i in o)if(o.hasOwnProperty(i)){var a=o[i];i==="children"?typeof a=="string"?n.textContent!==a&&(o.suppressHydrationWarning!==!0&&xs(n.textContent,a,e),s=["children",a]):typeof a=="number"&&n.textContent!==""+a&&(o.suppressHydrationWarning!==!0&&xs(n.textContent,a,e),s=["children",""+a]):Ln.hasOwnProperty(i)&&a!=null&&i==="onScroll"&&J("scroll",n)}switch(r){case"input":us(n),Fa(n,o,!0);break;case"textarea":us(n),Ua(n);break;case"select":case"option":break;default:typeof o.onClick=="function"&&(n.onclick=Ys)}n=s,t.updateQueue=n,n!==null&&(t.flags|=4)}else{i=s.nodeType===9?s:s.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Ec(r)),e==="http://www.w3.org/1999/xhtml"?r==="script"?(e=i.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof n.is=="string"?e=i.createElement(r,{is:n.is}):(e=i.createElement(r),r==="select"&&(i=e,n.multiple?i.multiple=!0:n.size&&(i.size=n.size))):e=i.createElementNS(e,r),e[yt]=t,e[Bn]=n,Xd(e,t,!1,!1),t.stateNode=e;e:{switch(i=Ol(r,n),r){case"dialog":J("cancel",e),J("close",e),s=n;break;case"iframe":case"object":case"embed":J("load",e),s=n;break;case"video":case"audio":for(s=0;s<jn.length;s++)J(jn[s],e);s=n;break;case"source":J("error",e),s=n;break;case"img":case"image":case"link":J("error",e),J("load",e),s=n;break;case"details":J("toggle",e),s=n;break;case"input":Ma(e,n),s=_l(e,n),J("invalid",e);break;case"option":s=n;break;case"select":e._wrapperState={wasMultiple:!!n.multiple},s=re({},n,{value:void 0}),J("invalid",e);break;case"textarea":Aa(e,n),s=Rl(e,n),J("invalid",e);break;default:s=n}Ll(r,s),a=s;for(o in a)if(a.hasOwnProperty(o)){var u=a[o];o==="style"?Tc(e,u):o==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,u!=null&&Pc(e,u)):o==="children"?typeof u=="string"?(r!=="textarea"||u!=="")&&On(e,u):typeof u=="number"&&On(e,""+u):o!=="suppressContentEditableWarning"&&o!=="suppressHydrationWarning"&&o!=="autoFocus"&&(Ln.hasOwnProperty(o)?u!=null&&o==="onScroll"&&J("scroll",e):u!=null&&Ri(e,o,u,i))}switch(r){case"input":us(e),Fa(e,n,!1);break;case"textarea":us(e),Ua(e);break;case"option":n.value!=null&&e.setAttribute("value",""+Zt(n.value));break;case"select":e.multiple=!!n.multiple,o=n.value,o!=null?Ir(e,!!n.multiple,o,!1):n.defaultValue!=null&&Ir(e,!!n.multiple,n.defaultValue,!0);break;default:typeof s.onClick=="function"&&(e.onclick=Ys)}switch(r){case"button":case"input":case"select":case"textarea":n=!!n.autoFocus;break e;case"img":n=!0;break e;default:n=!1}}n&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return ke(t),null;case 6:if(e&&t.stateNode!=null)ef(e,t,e.memoizedProps,n);else{if(typeof n!="string"&&t.stateNode===null)throw Error(P(166));if(r=fr(Wn.current),fr(Nt.current),ys(t)){if(n=t.stateNode,r=t.memoizedProps,n[yt]=t,(o=n.nodeValue!==r)&&(e=qe,e!==null))switch(e.tag){case 3:xs(n.nodeValue,r,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&xs(n.nodeValue,r,(e.mode&1)!==0)}o&&(t.flags|=4)}else n=(r.nodeType===9?r:r.ownerDocument).createTextNode(n),n[yt]=t,t.stateNode=n}return ke(t),null;case 13:if(G(ee),n=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(Z&&Ve!==null&&t.mode&1&&!(t.flags&128))yd(),Qr(),t.flags|=98560,o=!1;else if(o=ys(t),n!==null&&n.dehydrated!==null){if(e===null){if(!o)throw Error(P(318));if(o=t.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(P(317));o[yt]=t}else Qr(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;ke(t),o=!1}else lt!==null&&(pi(lt),lt=null),o=!0;if(!o)return t.flags&65536?t:null}return t.flags&128?(t.lanes=r,t):(n=n!==null,n!==(e!==null&&e.memoizedState!==null)&&n&&(t.child.flags|=8192,t.mode&1&&(e===null||ee.current&1?me===0&&(me=3):fa())),t.updateQueue!==null&&(t.flags|=4),ke(t),null);case 4:return Yr(),oi(e,t),e===null&&In(t.stateNode.containerInfo),ke(t),null;case 10:return Ji(t.type._context),ke(t),null;case 17:return Ie(t.type)&&Gs(),ke(t),null;case 19:if(G(ee),o=t.memoizedState,o===null)return ke(t),null;if(n=(t.flags&128)!==0,i=o.rendering,i===null)if(n)hn(o,!1);else{if(me!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(i=so(e),i!==null){for(t.flags|=128,hn(o,!1),n=i.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),t.subtreeFlags=0,n=r,r=t.child;r!==null;)o=r,e=n,o.flags&=14680066,i=o.alternate,i===null?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=i.childLanes,o.lanes=i.lanes,o.child=i.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=i.memoizedProps,o.memoizedState=i.memoizedState,o.updateQueue=i.updateQueue,o.type=i.type,e=i.dependencies,o.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),r=r.sibling;return Q(ee,ee.current&1|2),t.child}e=e.sibling}o.tail!==null&&le()>Xr&&(t.flags|=128,n=!0,hn(o,!1),t.lanes=4194304)}else{if(!n)if(e=so(i),e!==null){if(t.flags|=128,n=!0,r=e.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),hn(o,!0),o.tail===null&&o.tailMode==="hidden"&&!i.alternate&&!Z)return ke(t),null}else 2*le()-o.renderingStartTime>Xr&&r!==1073741824&&(t.flags|=128,n=!0,hn(o,!1),t.lanes=4194304);o.isBackwards?(i.sibling=t.child,t.child=i):(r=o.last,r!==null?r.sibling=i:t.child=i,o.last=i)}return o.tail!==null?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=le(),t.sibling=null,r=ee.current,Q(ee,n?r&1|2:r&1),t):(ke(t),null);case 22:case 23:return da(),n=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==n&&(t.flags|=8192),n&&t.mode&1?We&1073741824&&(ke(t),t.subtreeFlags&6&&(t.flags|=8192)):ke(t),null;case 24:return null;case 25:return null}throw Error(P(156,t.tag))}function ch(e,t){switch(Vi(t),t.tag){case 1:return Ie(t.type)&&Gs(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Yr(),G(De),G(Re),ea(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Zi(t),null;case 13:if(G(ee),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(P(340));Qr()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return G(ee),null;case 4:return Yr(),null;case 10:return Ji(t.type._context),null;case 22:case 23:return da(),null;case 24:return null;default:return null}}var Ns=!1,_e=!1,dh=typeof WeakSet=="function"?WeakSet:Set,O=null;function Ur(e,t){var r=e.ref;if(r!==null)if(typeof r=="function")try{r(null)}catch(n){ne(e,t,n)}else r.current=null}function li(e,t,r){try{r()}catch(n){ne(e,t,n)}}var ku=!1;function fh(e,t){if(Hl=Ks,e=od(),Hi(e)){if("selectionStart"in e)var r={start:e.selectionStart,end:e.selectionEnd};else e:{r=(r=e.ownerDocument)&&r.defaultView||window;var n=r.getSelection&&r.getSelection();if(n&&n.rangeCount!==0){r=n.anchorNode;var s=n.anchorOffset,o=n.focusNode;n=n.focusOffset;try{r.nodeType,o.nodeType}catch{r=null;break e}var i=0,a=-1,u=-1,c=0,f=0,d=e,g=null;t:for(;;){for(var b;d!==r||s!==0&&d.nodeType!==3||(a=i+s),d!==o||n!==0&&d.nodeType!==3||(u=i+n),d.nodeType===3&&(i+=d.nodeValue.length),(b=d.firstChild)!==null;)g=d,d=b;for(;;){if(d===e)break t;if(g===r&&++c===s&&(a=i),g===o&&++f===n&&(u=i),(b=d.nextSibling)!==null)break;d=g,g=d.parentNode}d=b}r=a===-1||u===-1?null:{start:a,end:u}}else r=null}r=r||{start:0,end:0}}else r=null;for(Wl={focusedElem:e,selectionRange:r},Ks=!1,O=t;O!==null;)if(t=O,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,O=e;else for(;O!==null;){t=O;try{var x=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(x!==null){var y=x.memoizedProps,S=x.memoizedState,m=t.stateNode,p=m.getSnapshotBeforeUpdate(t.elementType===t.type?y:st(t.type,y),S);m.__reactInternalSnapshotBeforeUpdate=p}break;case 3:var h=t.stateNode.containerInfo;h.nodeType===1?h.textContent="":h.nodeType===9&&h.documentElement&&h.removeChild(h.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(P(163))}}catch(N){ne(t,t.return,N)}if(e=t.sibling,e!==null){e.return=t.return,O=e;break}O=t.return}return x=ku,ku=!1,x}function Pn(e,t,r){var n=t.updateQueue;if(n=n!==null?n.lastEffect:null,n!==null){var s=n=n.next;do{if((s.tag&e)===e){var o=s.destroy;s.destroy=void 0,o!==void 0&&li(t,r,o)}s=s.next}while(s!==n)}}function _o(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var r=t=t.next;do{if((r.tag&e)===e){var n=r.create;r.destroy=n()}r=r.next}while(r!==t)}}function ii(e){var t=e.ref;if(t!==null){var r=e.stateNode;switch(e.tag){case 5:e=r;break;default:e=r}typeof t=="function"?t(e):t.current=e}}function tf(e){var t=e.alternate;t!==null&&(e.alternate=null,tf(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[yt],delete t[Bn],delete t[Kl],delete t[Qm],delete t[Jm])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function rf(e){return e.tag===5||e.tag===3||e.tag===4}function _u(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||rf(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function ai(e,t,r){var n=e.tag;if(n===5||n===6)e=e.stateNode,t?r.nodeType===8?r.parentNode.insertBefore(e,t):r.insertBefore(e,t):(r.nodeType===8?(t=r.parentNode,t.insertBefore(e,r)):(t=r,t.appendChild(e)),r=r._reactRootContainer,r!=null||t.onclick!==null||(t.onclick=Ys));else if(n!==4&&(e=e.child,e!==null))for(ai(e,t,r),e=e.sibling;e!==null;)ai(e,t,r),e=e.sibling}function ui(e,t,r){var n=e.tag;if(n===5||n===6)e=e.stateNode,t?r.insertBefore(e,t):r.appendChild(e);else if(n!==4&&(e=e.child,e!==null))for(ui(e,t,r),e=e.sibling;e!==null;)ui(e,t,r),e=e.sibling}var Ne=null,ot=!1;function At(e,t,r){for(r=r.child;r!==null;)nf(e,t,r),r=r.sibling}function nf(e,t,r){if(wt&&typeof wt.onCommitFiberUnmount=="function")try{wt.onCommitFiberUnmount(vo,r)}catch{}switch(r.tag){case 5:_e||Ur(r,t);case 6:var n=Ne,s=ot;Ne=null,At(e,t,r),Ne=n,ot=s,Ne!==null&&(ot?(e=Ne,r=r.stateNode,e.nodeType===8?e.parentNode.removeChild(r):e.removeChild(r)):Ne.removeChild(r.stateNode));break;case 18:Ne!==null&&(ot?(e=Ne,r=r.stateNode,e.nodeType===8?il(e.parentNode,r):e.nodeType===1&&il(e,r),Un(e)):il(Ne,r.stateNode));break;case 4:n=Ne,s=ot,Ne=r.stateNode.containerInfo,ot=!0,At(e,t,r),Ne=n,ot=s;break;case 0:case 11:case 14:case 15:if(!_e&&(n=r.updateQueue,n!==null&&(n=n.lastEffect,n!==null))){s=n=n.next;do{var o=s,i=o.destroy;o=o.tag,i!==void 0&&(o&2||o&4)&&li(r,t,i),s=s.next}while(s!==n)}At(e,t,r);break;case 1:if(!_e&&(Ur(r,t),n=r.stateNode,typeof n.componentWillUnmount=="function"))try{n.props=r.memoizedProps,n.state=r.memoizedState,n.componentWillUnmount()}catch(a){ne(r,t,a)}At(e,t,r);break;case 21:At(e,t,r);break;case 22:r.mode&1?(_e=(n=_e)||r.memoizedState!==null,At(e,t,r),_e=n):At(e,t,r);break;default:At(e,t,r)}}function Eu(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var r=e.stateNode;r===null&&(r=e.stateNode=new dh),t.forEach(function(n){var s=Nh.bind(null,e,n);r.has(n)||(r.add(n),n.then(s,s))})}}function nt(e,t){var r=t.deletions;if(r!==null)for(var n=0;n<r.length;n++){var s=r[n];try{var o=e,i=t,a=i;e:for(;a!==null;){switch(a.tag){case 5:Ne=a.stateNode,ot=!1;break e;case 3:Ne=a.stateNode.containerInfo,ot=!0;break e;case 4:Ne=a.stateNode.containerInfo,ot=!0;break e}a=a.return}if(Ne===null)throw Error(P(160));nf(o,i,s),Ne=null,ot=!1;var u=s.alternate;u!==null&&(u.return=null),s.return=null}catch(c){ne(s,t,c)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)sf(t,e),t=t.sibling}function sf(e,t){var r=e.alternate,n=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(nt(t,e),ht(e),n&4){try{Pn(3,e,e.return),_o(3,e)}catch(y){ne(e,e.return,y)}try{Pn(5,e,e.return)}catch(y){ne(e,e.return,y)}}break;case 1:nt(t,e),ht(e),n&512&&r!==null&&Ur(r,r.return);break;case 5:if(nt(t,e),ht(e),n&512&&r!==null&&Ur(r,r.return),e.flags&32){var s=e.stateNode;try{On(s,"")}catch(y){ne(e,e.return,y)}}if(n&4&&(s=e.stateNode,s!=null)){var o=e.memoizedProps,i=r!==null?r.memoizedProps:o,a=e.type,u=e.updateQueue;if(e.updateQueue=null,u!==null)try{a==="input"&&o.type==="radio"&&o.name!=null&&kc(s,o),Ol(a,i);var c=Ol(a,o);for(i=0;i<u.length;i+=2){var f=u[i],d=u[i+1];f==="style"?Tc(s,d):f==="dangerouslySetInnerHTML"?Pc(s,d):f==="children"?On(s,d):Ri(s,f,d,c)}switch(a){case"input":El(s,o);break;case"textarea":_c(s,o);break;case"select":var g=s._wrapperState.wasMultiple;s._wrapperState.wasMultiple=!!o.multiple;var b=o.value;b!=null?Ir(s,!!o.multiple,b,!1):g!==!!o.multiple&&(o.defaultValue!=null?Ir(s,!!o.multiple,o.defaultValue,!0):Ir(s,!!o.multiple,o.multiple?[]:"",!1))}s[Bn]=o}catch(y){ne(e,e.return,y)}}break;case 6:if(nt(t,e),ht(e),n&4){if(e.stateNode===null)throw Error(P(162));s=e.stateNode,o=e.memoizedProps;try{s.nodeValue=o}catch(y){ne(e,e.return,y)}}break;case 3:if(nt(t,e),ht(e),n&4&&r!==null&&r.memoizedState.isDehydrated)try{Un(t.containerInfo)}catch(y){ne(e,e.return,y)}break;case 4:nt(t,e),ht(e);break;case 13:nt(t,e),ht(e),s=e.child,s.flags&8192&&(o=s.memoizedState!==null,s.stateNode.isHidden=o,!o||s.alternate!==null&&s.alternate.memoizedState!==null||(ua=le())),n&4&&Eu(e);break;case 22:if(f=r!==null&&r.memoizedState!==null,e.mode&1?(_e=(c=_e)||f,nt(t,e),_e=c):nt(t,e),ht(e),n&8192){if(c=e.memoizedState!==null,(e.stateNode.isHidden=c)&&!f&&e.mode&1)for(O=e,f=e.child;f!==null;){for(d=O=f;O!==null;){switch(g=O,b=g.child,g.tag){case 0:case 11:case 14:case 15:Pn(4,g,g.return);break;case 1:Ur(g,g.return);var x=g.stateNode;if(typeof x.componentWillUnmount=="function"){n=g,r=g.return;try{t=n,x.props=t.memoizedProps,x.state=t.memoizedState,x.componentWillUnmount()}catch(y){ne(n,r,y)}}break;case 5:Ur(g,g.return);break;case 22:if(g.memoizedState!==null){Ru(d);continue}}b!==null?(b.return=g,O=b):Ru(d)}f=f.sibling}e:for(f=null,d=e;;){if(d.tag===5){if(f===null){f=d;try{s=d.stateNode,c?(o=s.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none"):(a=d.stateNode,u=d.memoizedProps.style,i=u!=null&&u.hasOwnProperty("display")?u.display:null,a.style.display=Rc("display",i))}catch(y){ne(e,e.return,y)}}}else if(d.tag===6){if(f===null)try{d.stateNode.nodeValue=c?"":d.memoizedProps}catch(y){ne(e,e.return,y)}}else if((d.tag!==22&&d.tag!==23||d.memoizedState===null||d===e)&&d.child!==null){d.child.return=d,d=d.child;continue}if(d===e)break e;for(;d.sibling===null;){if(d.return===null||d.return===e)break e;f===d&&(f=null),d=d.return}f===d&&(f=null),d.sibling.return=d.return,d=d.sibling}}break;case 19:nt(t,e),ht(e),n&4&&Eu(e);break;case 21:break;default:nt(t,e),ht(e)}}function ht(e){var t=e.flags;if(t&2){try{e:{for(var r=e.return;r!==null;){if(rf(r)){var n=r;break e}r=r.return}throw Error(P(160))}switch(n.tag){case 5:var s=n.stateNode;n.flags&32&&(On(s,""),n.flags&=-33);var o=_u(e);ui(e,o,s);break;case 3:case 4:var i=n.stateNode.containerInfo,a=_u(e);ai(e,a,i);break;default:throw Error(P(161))}}catch(u){ne(e,e.return,u)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function ph(e,t,r){O=e,of(e)}function of(e,t,r){for(var n=(e.mode&1)!==0;O!==null;){var s=O,o=s.child;if(s.tag===22&&n){var i=s.memoizedState!==null||Ns;if(!i){var a=s.alternate,u=a!==null&&a.memoizedState!==null||_e;a=Ns;var c=_e;if(Ns=i,(_e=u)&&!c)for(O=s;O!==null;)i=O,u=i.child,i.tag===22&&i.memoizedState!==null?Tu(s):u!==null?(u.return=i,O=u):Tu(s);for(;o!==null;)O=o,of(o),o=o.sibling;O=s,Ns=a,_e=c}Pu(e)}else s.subtreeFlags&8772&&o!==null?(o.return=s,O=o):Pu(e)}}function Pu(e){for(;O!==null;){var t=O;if(t.flags&8772){var r=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:_e||_o(5,t);break;case 1:var n=t.stateNode;if(t.flags&4&&!_e)if(r===null)n.componentDidMount();else{var s=t.elementType===t.type?r.memoizedProps:st(t.type,r.memoizedProps);n.componentDidUpdate(s,r.memoizedState,n.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;o!==null&&pu(t,o,n);break;case 3:var i=t.updateQueue;if(i!==null){if(r=null,t.child!==null)switch(t.child.tag){case 5:r=t.child.stateNode;break;case 1:r=t.child.stateNode}pu(t,i,r)}break;case 5:var a=t.stateNode;if(r===null&&t.flags&4){r=a;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&r.focus();break;case"img":u.src&&(r.src=u.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var c=t.alternate;if(c!==null){var f=c.memoizedState;if(f!==null){var d=f.dehydrated;d!==null&&Un(d)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(P(163))}_e||t.flags&512&&ii(t)}catch(g){ne(t,t.return,g)}}if(t===e){O=null;break}if(r=t.sibling,r!==null){r.return=t.return,O=r;break}O=t.return}}function Ru(e){for(;O!==null;){var t=O;if(t===e){O=null;break}var r=t.sibling;if(r!==null){r.return=t.return,O=r;break}O=t.return}}function Tu(e){for(;O!==null;){var t=O;try{switch(t.tag){case 0:case 11:case 15:var r=t.return;try{_o(4,t)}catch(u){ne(t,r,u)}break;case 1:var n=t.stateNode;if(typeof n.componentDidMount=="function"){var s=t.return;try{n.componentDidMount()}catch(u){ne(t,s,u)}}var o=t.return;try{ii(t)}catch(u){ne(t,o,u)}break;case 5:var i=t.return;try{ii(t)}catch(u){ne(t,i,u)}}}catch(u){ne(t,t.return,u)}if(t===e){O=null;break}var a=t.sibling;if(a!==null){a.return=t.return,O=a;break}O=t.return}}var mh=Math.ceil,io=Lt.ReactCurrentDispatcher,ia=Lt.ReactCurrentOwner,Ze=Lt.ReactCurrentBatchConfig,W=0,ye=null,ce=null,je=0,We=0,zr=rr(0),me=0,Qn=null,vr=0,Eo=0,aa=0,Rn=null,Ue=null,ua=0,Xr=1/0,jt=null,ao=!1,ci=null,Jt=null,js=!1,Bt=null,uo=0,Tn=0,di=null,Ms=-1,Fs=0;function Me(){return W&6?le():Ms!==-1?Ms:Ms=le()}function Yt(e){return e.mode&1?W&2&&je!==0?je&-je:Gm.transition!==null?(Fs===0&&(Fs=Hc()),Fs):(e=q,e!==0||(e=window.event,e=e===void 0?16:Yc(e.type)),e):1}function at(e,t,r,n){if(50<Tn)throw Tn=0,di=null,Error(P(185));Zn(e,r,n),(!(W&2)||e!==ye)&&(e===ye&&(!(W&2)&&(Eo|=r),me===4&&It(e,je)),$e(e,n),r===1&&W===0&&!(t.mode&1)&&(Xr=le()+500,So&&nr()))}function $e(e,t){var r=e.callbackNode;Gp(e,t);var n=qs(e,e===ye?je:0);if(n===0)r!==null&&Ia(r),e.callbackNode=null,e.callbackPriority=0;else if(t=n&-n,e.callbackPriority!==t){if(r!=null&&Ia(r),t===1)e.tag===0?Ym(Lu.bind(null,e)):hd(Lu.bind(null,e)),qm(function(){!(W&6)&&nr()}),r=null;else{switch(Wc(n)){case 1:r=Fi;break;case 4:r=$c;break;case 16:r=Vs;break;case 536870912:r=Bc;break;default:r=Vs}r=mf(r,lf.bind(null,e))}e.callbackPriority=t,e.callbackNode=r}}function lf(e,t){if(Ms=-1,Fs=0,W&6)throw Error(P(327));var r=e.callbackNode;if(Vr()&&e.callbackNode!==r)return null;var n=qs(e,e===ye?je:0);if(n===0)return null;if(n&30||n&e.expiredLanes||t)t=co(e,n);else{t=n;var s=W;W|=2;var o=uf();(ye!==e||je!==t)&&(jt=null,Xr=le()+500,mr(e,t));do try{xh();break}catch(a){af(e,a)}while(1);Qi(),io.current=o,W=s,ce!==null?t=0:(ye=null,je=0,t=me)}if(t!==0){if(t===2&&(s=zl(e),s!==0&&(n=s,t=fi(e,s))),t===1)throw r=Qn,mr(e,0),It(e,n),$e(e,le()),r;if(t===6)It(e,n);else{if(s=e.current.alternate,!(n&30)&&!hh(s)&&(t=co(e,n),t===2&&(o=zl(e),o!==0&&(n=o,t=fi(e,o))),t===1))throw r=Qn,mr(e,0),It(e,n),$e(e,le()),r;switch(e.finishedWork=s,e.finishedLanes=n,t){case 0:case 1:throw Error(P(345));case 2:ur(e,Ue,jt);break;case 3:if(It(e,n),(n&130023424)===n&&(t=ua+500-le(),10<t)){if(qs(e,0)!==0)break;if(s=e.suspendedLanes,(s&n)!==n){Me(),e.pingedLanes|=e.suspendedLanes&s;break}e.timeoutHandle=ql(ur.bind(null,e,Ue,jt),t);break}ur(e,Ue,jt);break;case 4:if(It(e,n),(n&4194240)===n)break;for(t=e.eventTimes,s=-1;0<n;){var i=31-it(n);o=1<<i,i=t[i],i>s&&(s=i),n&=~o}if(n=s,n=le()-n,n=(120>n?120:480>n?480:1080>n?1080:1920>n?1920:3e3>n?3e3:4320>n?4320:1960*mh(n/1960))-n,10<n){e.timeoutHandle=ql(ur.bind(null,e,Ue,jt),n);break}ur(e,Ue,jt);break;case 5:ur(e,Ue,jt);break;default:throw Error(P(329))}}}return $e(e,le()),e.callbackNode===r?lf.bind(null,e):null}function fi(e,t){var r=Rn;return e.current.memoizedState.isDehydrated&&(mr(e,t).flags|=256),e=co(e,t),e!==2&&(t=Ue,Ue=r,t!==null&&pi(t)),e}function pi(e){Ue===null?Ue=e:Ue.push.apply(Ue,e)}function hh(e){for(var t=e;;){if(t.flags&16384){var r=t.updateQueue;if(r!==null&&(r=r.stores,r!==null))for(var n=0;n<r.length;n++){var s=r[n],o=s.getSnapshot;s=s.value;try{if(!dt(o(),s))return!1}catch{return!1}}}if(r=t.child,t.subtreeFlags&16384&&r!==null)r.return=t,t=r;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function It(e,t){for(t&=~aa,t&=~Eo,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var r=31-it(t),n=1<<r;e[r]=-1,t&=~n}}function Lu(e){if(W&6)throw Error(P(327));Vr();var t=qs(e,0);if(!(t&1))return $e(e,le()),null;var r=co(e,t);if(e.tag!==0&&r===2){var n=zl(e);n!==0&&(t=n,r=fi(e,n))}if(r===1)throw r=Qn,mr(e,0),It(e,t),$e(e,le()),r;if(r===6)throw Error(P(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,ur(e,Ue,jt),$e(e,le()),null}function ca(e,t){var r=W;W|=1;try{return e(t)}finally{W=r,W===0&&(Xr=le()+500,So&&nr())}}function wr(e){Bt!==null&&Bt.tag===0&&!(W&6)&&Vr();var t=W;W|=1;var r=Ze.transition,n=q;try{if(Ze.transition=null,q=1,e)return e()}finally{q=n,Ze.transition=r,W=t,!(W&6)&&nr()}}function da(){We=zr.current,G(zr)}function mr(e,t){e.finishedWork=null,e.finishedLanes=0;var r=e.timeoutHandle;if(r!==-1&&(e.timeoutHandle=-1,Vm(r)),ce!==null)for(r=ce.return;r!==null;){var n=r;switch(Vi(n),n.tag){case 1:n=n.type.childContextTypes,n!=null&&Gs();break;case 3:Yr(),G(De),G(Re),ea();break;case 5:Zi(n);break;case 4:Yr();break;case 13:G(ee);break;case 19:G(ee);break;case 10:Ji(n.type._context);break;case 22:case 23:da()}r=r.return}if(ye=e,ce=e=Gt(e.current,null),je=We=t,me=0,Qn=null,aa=Eo=vr=0,Ue=Rn=null,dr!==null){for(t=0;t<dr.length;t++)if(r=dr[t],n=r.interleaved,n!==null){r.interleaved=null;var s=n.next,o=r.pending;if(o!==null){var i=o.next;o.next=s,n.next=i}r.pending=n}dr=null}return e}function af(e,t){do{var r=ce;try{if(Qi(),Ts.current=lo,oo){for(var n=te.memoizedState;n!==null;){var s=n.queue;s!==null&&(s.pending=null),n=n.next}oo=!1}if(yr=0,xe=pe=te=null,En=!1,Vn=0,ia.current=null,r===null||r.return===null){me=1,Qn=t,ce=null;break}e:{var o=e,i=r.return,a=r,u=t;if(t=je,a.flags|=32768,u!==null&&typeof u=="object"&&typeof u.then=="function"){var c=u,f=a,d=f.tag;if(!(f.mode&1)&&(d===0||d===11||d===15)){var g=f.alternate;g?(f.updateQueue=g.updateQueue,f.memoizedState=g.memoizedState,f.lanes=g.lanes):(f.updateQueue=null,f.memoizedState=null)}var b=vu(i);if(b!==null){b.flags&=-257,wu(b,i,a,o,t),b.mode&1&&yu(o,c,t),t=b,u=c;var x=t.updateQueue;if(x===null){var y=new Set;y.add(u),t.updateQueue=y}else x.add(u);break e}else{if(!(t&1)){yu(o,c,t),fa();break e}u=Error(P(426))}}else if(Z&&a.mode&1){var S=vu(i);if(S!==null){!(S.flags&65536)&&(S.flags|=256),wu(S,i,a,o,t),qi(Gr(u,a));break e}}o=u=Gr(u,a),me!==4&&(me=2),Rn===null?Rn=[o]:Rn.push(o),o=i;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t;var m=Wd(o,u,t);fu(o,m);break e;case 1:a=u;var p=o.type,h=o.stateNode;if(!(o.flags&128)&&(typeof p.getDerivedStateFromError=="function"||h!==null&&typeof h.componentDidCatch=="function"&&(Jt===null||!Jt.has(h)))){o.flags|=65536,t&=-t,o.lanes|=t;var N=Vd(o,a,t);fu(o,N);break e}}o=o.return}while(o!==null)}df(r)}catch(w){t=w,ce===r&&r!==null&&(ce=r=r.return);continue}break}while(1)}function uf(){var e=io.current;return io.current=lo,e===null?lo:e}function fa(){(me===0||me===3||me===2)&&(me=4),ye===null||!(vr&268435455)&&!(Eo&268435455)||It(ye,je)}function co(e,t){var r=W;W|=2;var n=uf();(ye!==e||je!==t)&&(jt=null,mr(e,t));do try{gh();break}catch(s){af(e,s)}while(1);if(Qi(),W=r,io.current=n,ce!==null)throw Error(P(261));return ye=null,je=0,me}function gh(){for(;ce!==null;)cf(ce)}function xh(){for(;ce!==null&&!Bp();)cf(ce)}function cf(e){var t=pf(e.alternate,e,We);e.memoizedProps=e.pendingProps,t===null?df(e):ce=t,ia.current=null}function df(e){var t=e;do{var r=t.alternate;if(e=t.return,t.flags&32768){if(r=ch(r,t),r!==null){r.flags&=32767,ce=r;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{me=6,ce=null;return}}else if(r=uh(r,t,We),r!==null){ce=r;return}if(t=t.sibling,t!==null){ce=t;return}ce=t=e}while(t!==null);me===0&&(me=5)}function ur(e,t,r){var n=q,s=Ze.transition;try{Ze.transition=null,q=1,yh(e,t,r,n)}finally{Ze.transition=s,q=n}return null}function yh(e,t,r,n){do Vr();while(Bt!==null);if(W&6)throw Error(P(327));r=e.finishedWork;var s=e.finishedLanes;if(r===null)return null;if(e.finishedWork=null,e.finishedLanes=0,r===e.current)throw Error(P(177));e.callbackNode=null,e.callbackPriority=0;var o=r.lanes|r.childLanes;if(Xp(e,o),e===ye&&(ce=ye=null,je=0),!(r.subtreeFlags&2064)&&!(r.flags&2064)||js||(js=!0,mf(Vs,function(){return Vr(),null})),o=(r.flags&15990)!==0,r.subtreeFlags&15990||o){o=Ze.transition,Ze.transition=null;var i=q;q=1;var a=W;W|=4,ia.current=null,fh(e,r),sf(r,e),zm(Wl),Ks=!!Hl,Wl=Hl=null,e.current=r,ph(r),Hp(),W=a,q=i,Ze.transition=o}else e.current=r;if(js&&(js=!1,Bt=e,uo=s),o=e.pendingLanes,o===0&&(Jt=null),qp(r.stateNode),$e(e,le()),t!==null)for(n=e.onRecoverableError,r=0;r<t.length;r++)s=t[r],n(s.value,{componentStack:s.stack,digest:s.digest});if(ao)throw ao=!1,e=ci,ci=null,e;return uo&1&&e.tag!==0&&Vr(),o=e.pendingLanes,o&1?e===di?Tn++:(Tn=0,di=e):Tn=0,nr(),null}function Vr(){if(Bt!==null){var e=Wc(uo),t=Ze.transition,r=q;try{if(Ze.transition=null,q=16>e?16:e,Bt===null)var n=!1;else{if(e=Bt,Bt=null,uo=0,W&6)throw Error(P(331));var s=W;for(W|=4,O=e.current;O!==null;){var o=O,i=o.child;if(O.flags&16){var a=o.deletions;if(a!==null){for(var u=0;u<a.length;u++){var c=a[u];for(O=c;O!==null;){var f=O;switch(f.tag){case 0:case 11:case 15:Pn(8,f,o)}var d=f.child;if(d!==null)d.return=f,O=d;else for(;O!==null;){f=O;var g=f.sibling,b=f.return;if(tf(f),f===c){O=null;break}if(g!==null){g.return=b,O=g;break}O=b}}}var x=o.alternate;if(x!==null){var y=x.child;if(y!==null){x.child=null;do{var S=y.sibling;y.sibling=null,y=S}while(y!==null)}}O=o}}if(o.subtreeFlags&2064&&i!==null)i.return=o,O=i;else e:for(;O!==null;){if(o=O,o.flags&2048)switch(o.tag){case 0:case 11:case 15:Pn(9,o,o.return)}var m=o.sibling;if(m!==null){m.return=o.return,O=m;break e}O=o.return}}var p=e.current;for(O=p;O!==null;){i=O;var h=i.child;if(i.subtreeFlags&2064&&h!==null)h.return=i,O=h;else e:for(i=p;O!==null;){if(a=O,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:_o(9,a)}}catch(w){ne(a,a.return,w)}if(a===i){O=null;break e}var N=a.sibling;if(N!==null){N.return=a.return,O=N;break e}O=a.return}}if(W=s,nr(),wt&&typeof wt.onPostCommitFiberRoot=="function")try{wt.onPostCommitFiberRoot(vo,e)}catch{}n=!0}return n}finally{q=r,Ze.transition=t}}return!1}function Ou(e,t,r){t=Gr(r,t),t=Wd(e,t,1),e=Qt(e,t,1),t=Me(),e!==null&&(Zn(e,1,t),$e(e,t))}function ne(e,t,r){if(e.tag===3)Ou(e,e,r);else for(;t!==null;){if(t.tag===3){Ou(t,e,r);break}else if(t.tag===1){var n=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof n.componentDidCatch=="function"&&(Jt===null||!Jt.has(n))){e=Gr(r,e),e=Vd(t,e,1),t=Qt(t,e,1),e=Me(),t!==null&&(Zn(t,1,e),$e(t,e));break}}t=t.return}}function vh(e,t,r){var n=e.pingCache;n!==null&&n.delete(t),t=Me(),e.pingedLanes|=e.suspendedLanes&r,ye===e&&(je&r)===r&&(me===4||me===3&&(je&130023424)===je&&500>le()-ua?mr(e,0):aa|=r),$e(e,t)}function ff(e,t){t===0&&(e.mode&1?(t=fs,fs<<=1,!(fs&130023424)&&(fs=4194304)):t=1);var r=Me();e=Rt(e,t),e!==null&&(Zn(e,t,r),$e(e,r))}function wh(e){var t=e.memoizedState,r=0;t!==null&&(r=t.retryLane),ff(e,r)}function Nh(e,t){var r=0;switch(e.tag){case 13:var n=e.stateNode,s=e.memoizedState;s!==null&&(r=s.retryLane);break;case 19:n=e.stateNode;break;default:throw Error(P(314))}n!==null&&n.delete(t),ff(e,r)}var pf;pf=function(e,t,r){if(e!==null)if(e.memoizedProps!==t.pendingProps||De.current)ze=!0;else{if(!(e.lanes&r)&&!(t.flags&128))return ze=!1,ah(e,t,r);ze=!!(e.flags&131072)}else ze=!1,Z&&t.flags&1048576&&gd(t,eo,t.index);switch(t.lanes=0,t.tag){case 2:var n=t.type;Os(e,t),e=t.pendingProps;var s=Kr(t,Re.current);Wr(t,r),s=ra(null,t,n,e,s,r);var o=na();return t.flags|=1,typeof s=="object"&&s!==null&&typeof s.render=="function"&&s.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Ie(n)?(o=!0,Xs(t)):o=!1,t.memoizedState=s.state!==null&&s.state!==void 0?s.state:null,Gi(t),s.updater=ko,t.stateNode=s,s._reactInternals=t,Zl(t,n,e,r),t=ri(null,t,n,!0,o,r)):(t.tag=0,Z&&o&&Wi(t),Oe(null,t,s,r),t=t.child),t;case 16:n=t.elementType;e:{switch(Os(e,t),e=t.pendingProps,s=n._init,n=s(n._payload),t.type=n,s=t.tag=bh(n),e=st(n,e),s){case 0:t=ti(null,t,n,e,r);break e;case 1:t=bu(null,t,n,e,r);break e;case 11:t=Nu(null,t,n,e,r);break e;case 14:t=ju(null,t,n,st(n.type,e),r);break e}throw Error(P(306,n,""))}return t;case 0:return n=t.type,s=t.pendingProps,s=t.elementType===n?s:st(n,s),ti(e,t,n,s,r);case 1:return n=t.type,s=t.pendingProps,s=t.elementType===n?s:st(n,s),bu(e,t,n,s,r);case 3:e:{if(Jd(t),e===null)throw Error(P(387));n=t.pendingProps,o=t.memoizedState,s=o.element,jd(e,t),no(t,n,null,r);var i=t.memoizedState;if(n=i.element,o.isDehydrated)if(o={element:n,isDehydrated:!1,cache:i.cache,pendingSuspenseBoundaries:i.pendingSuspenseBoundaries,transitions:i.transitions},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){s=Gr(Error(P(423)),t),t=Su(e,t,n,r,s);break e}else if(n!==s){s=Gr(Error(P(424)),t),t=Su(e,t,n,r,s);break e}else for(Ve=Kt(t.stateNode.containerInfo.firstChild),qe=t,Z=!0,lt=null,r=wd(t,null,n,r),t.child=r;r;)r.flags=r.flags&-3|4096,r=r.sibling;else{if(Qr(),n===s){t=Tt(e,t,r);break e}Oe(e,t,n,r)}t=t.child}return t;case 5:return bd(t),e===null&&Yl(t),n=t.type,s=t.pendingProps,o=e!==null?e.memoizedProps:null,i=s.children,Vl(n,s)?i=null:o!==null&&Vl(n,o)&&(t.flags|=32),Qd(e,t),Oe(e,t,i,r),t.child;case 6:return e===null&&Yl(t),null;case 13:return Yd(e,t,r);case 4:return Xi(t,t.stateNode.containerInfo),n=t.pendingProps,e===null?t.child=Jr(t,null,n,r):Oe(e,t,n,r),t.child;case 11:return n=t.type,s=t.pendingProps,s=t.elementType===n?s:st(n,s),Nu(e,t,n,s,r);case 7:return Oe(e,t,t.pendingProps,r),t.child;case 8:return Oe(e,t,t.pendingProps.children,r),t.child;case 12:return Oe(e,t,t.pendingProps.children,r),t.child;case 10:e:{if(n=t.type._context,s=t.pendingProps,o=t.memoizedProps,i=s.value,Q(to,n._currentValue),n._currentValue=i,o!==null)if(dt(o.value,i)){if(o.children===s.children&&!De.current){t=Tt(e,t,r);break e}}else for(o=t.child,o!==null&&(o.return=t);o!==null;){var a=o.dependencies;if(a!==null){i=o.child;for(var u=a.firstContext;u!==null;){if(u.context===n){if(o.tag===1){u=_t(-1,r&-r),u.tag=2;var c=o.updateQueue;if(c!==null){c=c.shared;var f=c.pending;f===null?u.next=u:(u.next=f.next,f.next=u),c.pending=u}}o.lanes|=r,u=o.alternate,u!==null&&(u.lanes|=r),Gl(o.return,r,t),a.lanes|=r;break}u=u.next}}else if(o.tag===10)i=o.type===t.type?null:o.child;else if(o.tag===18){if(i=o.return,i===null)throw Error(P(341));i.lanes|=r,a=i.alternate,a!==null&&(a.lanes|=r),Gl(i,r,t),i=o.sibling}else i=o.child;if(i!==null)i.return=o;else for(i=o;i!==null;){if(i===t){i=null;break}if(o=i.sibling,o!==null){o.return=i.return,i=o;break}i=i.return}o=i}Oe(e,t,s.children,r),t=t.child}return t;case 9:return s=t.type,n=t.pendingProps.children,Wr(t,r),s=et(s),n=n(s),t.flags|=1,Oe(e,t,n,r),t.child;case 14:return n=t.type,s=st(n,t.pendingProps),s=st(n.type,s),ju(e,t,n,s,r);case 15:return qd(e,t,t.type,t.pendingProps,r);case 17:return n=t.type,s=t.pendingProps,s=t.elementType===n?s:st(n,s),Os(e,t),t.tag=1,Ie(n)?(e=!0,Xs(t)):e=!1,Wr(t,r),Hd(t,n,s),Zl(t,n,s,r),ri(null,t,n,!0,e,r);case 19:return Gd(e,t,r);case 22:return Kd(e,t,r)}throw Error(P(156,t.tag))};function mf(e,t){return Ic(e,t)}function jh(e,t,r,n){this.tag=e,this.key=r,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=n,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Xe(e,t,r,n){return new jh(e,t,r,n)}function pa(e){return e=e.prototype,!(!e||!e.isReactComponent)}function bh(e){if(typeof e=="function")return pa(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Li)return 11;if(e===Oi)return 14}return 2}function Gt(e,t){var r=e.alternate;return r===null?(r=Xe(e.tag,t,e.key,e.mode),r.elementType=e.elementType,r.type=e.type,r.stateNode=e.stateNode,r.alternate=e,e.alternate=r):(r.pendingProps=t,r.type=e.type,r.flags=0,r.subtreeFlags=0,r.deletions=null),r.flags=e.flags&14680064,r.childLanes=e.childLanes,r.lanes=e.lanes,r.child=e.child,r.memoizedProps=e.memoizedProps,r.memoizedState=e.memoizedState,r.updateQueue=e.updateQueue,t=e.dependencies,r.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},r.sibling=e.sibling,r.index=e.index,r.ref=e.ref,r}function As(e,t,r,n,s,o){var i=2;if(n=e,typeof e=="function")pa(e)&&(i=1);else if(typeof e=="string")i=5;else e:switch(e){case Er:return hr(r.children,s,o,t);case Ti:i=8,s|=8;break;case bl:return e=Xe(12,r,t,s|2),e.elementType=bl,e.lanes=o,e;case Sl:return e=Xe(13,r,t,s),e.elementType=Sl,e.lanes=o,e;case Cl:return e=Xe(19,r,t,s),e.elementType=Cl,e.lanes=o,e;case bc:return Po(r,s,o,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Nc:i=10;break e;case jc:i=9;break e;case Li:i=11;break e;case Oi:i=14;break e;case Ut:i=16,n=null;break e}throw Error(P(130,e==null?e:typeof e,""))}return t=Xe(i,r,t,s),t.elementType=e,t.type=n,t.lanes=o,t}function hr(e,t,r,n){return e=Xe(7,e,n,t),e.lanes=r,e}function Po(e,t,r,n){return e=Xe(22,e,n,t),e.elementType=bc,e.lanes=r,e.stateNode={isHidden:!1},e}function hl(e,t,r){return e=Xe(6,e,null,t),e.lanes=r,e}function gl(e,t,r){return t=Xe(4,e.children!==null?e.children:[],e.key,t),t.lanes=r,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Sh(e,t,r,n,s){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Yo(0),this.expirationTimes=Yo(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Yo(0),this.identifierPrefix=n,this.onRecoverableError=s,this.mutableSourceEagerHydrationData=null}function ma(e,t,r,n,s,o,i,a,u){return e=new Sh(e,t,r,a,u),t===1?(t=1,o===!0&&(t|=8)):t=0,o=Xe(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:n,isDehydrated:r,cache:null,transitions:null,pendingSuspenseBoundaries:null},Gi(o),e}function Ch(e,t,r){var n=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:_r,key:n==null?null:""+n,children:e,containerInfo:t,implementation:r}}function hf(e){if(!e)return er;e=e._reactInternals;e:{if(Sr(e)!==e||e.tag!==1)throw Error(P(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Ie(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(P(171))}if(e.tag===1){var r=e.type;if(Ie(r))return md(e,r,t)}return t}function gf(e,t,r,n,s,o,i,a,u){return e=ma(r,n,!0,e,s,o,i,a,u),e.context=hf(null),r=e.current,n=Me(),s=Yt(r),o=_t(n,s),o.callback=t??null,Qt(r,o,s),e.current.lanes=s,Zn(e,s,n),$e(e,n),e}function Ro(e,t,r,n){var s=t.current,o=Me(),i=Yt(s);return r=hf(r),t.context===null?t.context=r:t.pendingContext=r,t=_t(o,i),t.payload={element:e},n=n===void 0?null:n,n!==null&&(t.callback=n),e=Qt(s,t,i),e!==null&&(at(e,s,i,o),Rs(e,s,i)),i}function fo(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Mu(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var r=e.retryLane;e.retryLane=r!==0&&r<t?r:t}}function ha(e,t){Mu(e,t),(e=e.alternate)&&Mu(e,t)}function kh(){return null}var xf=typeof reportError=="function"?reportError:function(e){console.error(e)};function ga(e){this._internalRoot=e}To.prototype.render=ga.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(P(409));Ro(e,t,null,null)};To.prototype.unmount=ga.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;wr(function(){Ro(null,e,null,null)}),t[Pt]=null}};function To(e){this._internalRoot=e}To.prototype.unstable_scheduleHydration=function(e){if(e){var t=Kc();e={blockedOn:null,target:e,priority:t};for(var r=0;r<Dt.length&&t!==0&&t<Dt[r].priority;r++);Dt.splice(r,0,e),r===0&&Jc(e)}};function xa(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Lo(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Fu(){}function _h(e,t,r,n,s){if(s){if(typeof n=="function"){var o=n;n=function(){var c=fo(i);o.call(c)}}var i=gf(t,n,e,0,null,!1,!1,"",Fu);return e._reactRootContainer=i,e[Pt]=i.current,In(e.nodeType===8?e.parentNode:e),wr(),i}for(;s=e.lastChild;)e.removeChild(s);if(typeof n=="function"){var a=n;n=function(){var c=fo(u);a.call(c)}}var u=ma(e,0,!1,null,null,!1,!1,"",Fu);return e._reactRootContainer=u,e[Pt]=u.current,In(e.nodeType===8?e.parentNode:e),wr(function(){Ro(t,u,r,n)}),u}function Oo(e,t,r,n,s){var o=r._reactRootContainer;if(o){var i=o;if(typeof s=="function"){var a=s;s=function(){var u=fo(i);a.call(u)}}Ro(t,i,e,s)}else i=_h(r,t,e,s,n);return fo(i)}Vc=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var r=Nn(t.pendingLanes);r!==0&&(Ai(t,r|1),$e(t,le()),!(W&6)&&(Xr=le()+500,nr()))}break;case 13:wr(function(){var n=Rt(e,1);if(n!==null){var s=Me();at(n,e,1,s)}}),ha(e,1)}};Ui=function(e){if(e.tag===13){var t=Rt(e,134217728);if(t!==null){var r=Me();at(t,e,134217728,r)}ha(e,134217728)}};qc=function(e){if(e.tag===13){var t=Yt(e),r=Rt(e,t);if(r!==null){var n=Me();at(r,e,t,n)}ha(e,t)}};Kc=function(){return q};Qc=function(e,t){var r=q;try{return q=e,t()}finally{q=r}};Fl=function(e,t,r){switch(t){case"input":if(El(e,r),t=r.name,r.type==="radio"&&t!=null){for(r=e;r.parentNode;)r=r.parentNode;for(r=r.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<r.length;t++){var n=r[t];if(n!==e&&n.form===e.form){var s=bo(n);if(!s)throw Error(P(90));Cc(n),El(n,s)}}}break;case"textarea":_c(e,r);break;case"select":t=r.value,t!=null&&Ir(e,!!r.multiple,t,!1)}};Mc=ca;Fc=wr;var Eh={usingClientEntryPoint:!1,Events:[ts,Lr,bo,Lc,Oc,ca]},gn={findFiberByHostInstance:cr,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Ph={bundleType:gn.bundleType,version:gn.version,rendererPackageName:gn.rendererPackageName,rendererConfig:gn.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Lt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=zc(e),e===null?null:e.stateNode},findFiberByHostInstance:gn.findFiberByHostInstance||kh,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var bs=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!bs.isDisabled&&bs.supportsFiber)try{vo=bs.inject(Ph),wt=bs}catch{}}Qe.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Eh;Qe.createPortal=function(e,t){var r=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!xa(t))throw Error(P(200));return Ch(e,t,null,r)};Qe.createRoot=function(e,t){if(!xa(e))throw Error(P(299));var r=!1,n="",s=xf;return t!=null&&(t.unstable_strictMode===!0&&(r=!0),t.identifierPrefix!==void 0&&(n=t.identifierPrefix),t.onRecoverableError!==void 0&&(s=t.onRecoverableError)),t=ma(e,1,!1,null,null,r,!1,n,s),e[Pt]=t.current,In(e.nodeType===8?e.parentNode:e),new ga(t)};Qe.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(P(188)):(e=Object.keys(e).join(","),Error(P(268,e)));return e=zc(t),e=e===null?null:e.stateNode,e};Qe.flushSync=function(e){return wr(e)};Qe.hydrate=function(e,t,r){if(!Lo(t))throw Error(P(200));return Oo(null,e,t,!0,r)};Qe.hydrateRoot=function(e,t,r){if(!xa(e))throw Error(P(405));var n=r!=null&&r.hydratedSources||null,s=!1,o="",i=xf;if(r!=null&&(r.unstable_strictMode===!0&&(s=!0),r.identifierPrefix!==void 0&&(o=r.identifierPrefix),r.onRecoverableError!==void 0&&(i=r.onRecoverableError)),t=gf(t,null,e,1,r??null,s,!1,o,i),e[Pt]=t.current,In(e),n)for(e=0;e<n.length;e++)r=n[e],s=r._getVersion,s=s(r._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[r,s]:t.mutableSourceEagerHydrationData.push(r,s);return new To(t)};Qe.render=function(e,t,r){if(!Lo(t))throw Error(P(200));return Oo(null,e,t,!1,r)};Qe.unmountComponentAtNode=function(e){if(!Lo(e))throw Error(P(40));return e._reactRootContainer?(wr(function(){Oo(null,null,e,!1,function(){e._reactRootContainer=null,e[Pt]=null})}),!0):!1};Qe.unstable_batchedUpdates=ca;Qe.unstable_renderSubtreeIntoContainer=function(e,t,r,n){if(!Lo(r))throw Error(P(200));if(e==null||e._reactInternals===void 0)throw Error(P(38));return Oo(e,t,r,!1,n)};Qe.version="18.3.1-next-f1338f8080-20240426";function yf(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(yf)}catch(e){console.error(e)}}yf(),xc.exports=Qe;var Rh=xc.exports,vf,Au=Rh;vf=Au.createRoot,Au.hydrateRoot;/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Jn(){return Jn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Jn.apply(this,arguments)}var Ht;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(Ht||(Ht={}));const Uu="popstate";function Th(e){e===void 0&&(e={});function t(n,s){let{pathname:o,search:i,hash:a}=n.location;return mi("",{pathname:o,search:i,hash:a},s.state&&s.state.usr||null,s.state&&s.state.key||"default")}function r(n,s){return typeof s=="string"?s:po(s)}return Oh(t,r,null,e)}function ie(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function wf(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function Lh(){return Math.random().toString(36).substr(2,8)}function zu(e,t){return{usr:e.state,key:e.key,idx:t}}function mi(e,t,r,n){return r===void 0&&(r=null),Jn({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?rn(t):t,{state:r,key:t&&t.key||n||Lh()})}function po(e){let{pathname:t="/",search:r="",hash:n=""}=e;return r&&r!=="?"&&(t+=r.charAt(0)==="?"?r:"?"+r),n&&n!=="#"&&(t+=n.charAt(0)==="#"?n:"#"+n),t}function rn(e){let t={};if(e){let r=e.indexOf("#");r>=0&&(t.hash=e.substr(r),e=e.substr(0,r));let n=e.indexOf("?");n>=0&&(t.search=e.substr(n),e=e.substr(0,n)),e&&(t.pathname=e)}return t}function Oh(e,t,r,n){n===void 0&&(n={});let{window:s=document.defaultView,v5Compat:o=!1}=n,i=s.history,a=Ht.Pop,u=null,c=f();c==null&&(c=0,i.replaceState(Jn({},i.state,{idx:c}),""));function f(){return(i.state||{idx:null}).idx}function d(){a=Ht.Pop;let S=f(),m=S==null?null:S-c;c=S,u&&u({action:a,location:y.location,delta:m})}function g(S,m){a=Ht.Push;let p=mi(y.location,S,m);r&&r(p,S),c=f()+1;let h=zu(p,c),N=y.createHref(p);try{i.pushState(h,"",N)}catch(w){if(w instanceof DOMException&&w.name==="DataCloneError")throw w;s.location.assign(N)}o&&u&&u({action:a,location:y.location,delta:1})}function b(S,m){a=Ht.Replace;let p=mi(y.location,S,m);r&&r(p,S),c=f();let h=zu(p,c),N=y.createHref(p);i.replaceState(h,"",N),o&&u&&u({action:a,location:y.location,delta:0})}function x(S){let m=s.location.origin!=="null"?s.location.origin:s.location.href,p=typeof S=="string"?S:po(S);return p=p.replace(/ $/,"%20"),ie(m,"No window.location.(origin|href) available to create URL for href: "+p),new URL(p,m)}let y={get action(){return a},get location(){return e(s,i)},listen(S){if(u)throw new Error("A history only accepts one active listener");return s.addEventListener(Uu,d),u=S,()=>{s.removeEventListener(Uu,d),u=null}},createHref(S){return t(s,S)},createURL:x,encodeLocation(S){let m=x(S);return{pathname:m.pathname,search:m.search,hash:m.hash}},push:g,replace:b,go(S){return i.go(S)}};return y}var Du;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(Du||(Du={}));function Mh(e,t,r){return r===void 0&&(r="/"),Fh(e,t,r,!1)}function Fh(e,t,r,n){let s=typeof t=="string"?rn(t):t,o=ya(s.pathname||"/",r);if(o==null)return null;let i=Nf(e);Ah(i);let a=null;for(let u=0;a==null&&u<i.length;++u){let c=Kh(o);a=Vh(i[u],c,n)}return a}function Nf(e,t,r,n){t===void 0&&(t=[]),r===void 0&&(r=[]),n===void 0&&(n="");let s=(o,i,a)=>{let u={relativePath:a===void 0?o.path||"":a,caseSensitive:o.caseSensitive===!0,childrenIndex:i,route:o};u.relativePath.startsWith("/")&&(ie(u.relativePath.startsWith(n),'Absolute route path "'+u.relativePath+'" nested under path '+('"'+n+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),u.relativePath=u.relativePath.slice(n.length));let c=Xt([n,u.relativePath]),f=r.concat(u);o.children&&o.children.length>0&&(ie(o.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+c+'".')),Nf(o.children,t,f,c)),!(o.path==null&&!o.index)&&t.push({path:c,score:Hh(c,o.index),routesMeta:f})};return e.forEach((o,i)=>{var a;if(o.path===""||!((a=o.path)!=null&&a.includes("?")))s(o,i);else for(let u of jf(o.path))s(o,i,u)}),t}function jf(e){let t=e.split("/");if(t.length===0)return[];let[r,...n]=t,s=r.endsWith("?"),o=r.replace(/\?$/,"");if(n.length===0)return s?[o,""]:[o];let i=jf(n.join("/")),a=[];return a.push(...i.map(u=>u===""?o:[o,u].join("/"))),s&&a.push(...i),a.map(u=>e.startsWith("/")&&u===""?"/":u)}function Ah(e){e.sort((t,r)=>t.score!==r.score?r.score-t.score:Wh(t.routesMeta.map(n=>n.childrenIndex),r.routesMeta.map(n=>n.childrenIndex)))}const Uh=/^:[\w-]+$/,zh=3,Dh=2,Ih=1,$h=10,Bh=-2,Iu=e=>e==="*";function Hh(e,t){let r=e.split("/"),n=r.length;return r.some(Iu)&&(n+=Bh),t&&(n+=Dh),r.filter(s=>!Iu(s)).reduce((s,o)=>s+(Uh.test(o)?zh:o===""?Ih:$h),n)}function Wh(e,t){return e.length===t.length&&e.slice(0,-1).every((n,s)=>n===t[s])?e[e.length-1]-t[t.length-1]:0}function Vh(e,t,r){r===void 0&&(r=!1);let{routesMeta:n}=e,s={},o="/",i=[];for(let a=0;a<n.length;++a){let u=n[a],c=a===n.length-1,f=o==="/"?t:t.slice(o.length)||"/",d=$u({path:u.relativePath,caseSensitive:u.caseSensitive,end:c},f),g=u.route;if(!d&&c&&r&&!n[n.length-1].route.index&&(d=$u({path:u.relativePath,caseSensitive:u.caseSensitive,end:!1},f)),!d)return null;Object.assign(s,d.params),i.push({params:s,pathname:Xt([o,d.pathname]),pathnameBase:Gh(Xt([o,d.pathnameBase])),route:g}),d.pathnameBase!=="/"&&(o=Xt([o,d.pathnameBase]))}return i}function $u(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[r,n]=qh(e.path,e.caseSensitive,e.end),s=t.match(r);if(!s)return null;let o=s[0],i=o.replace(/(.)\/+$/,"$1"),a=s.slice(1);return{params:n.reduce((c,f,d)=>{let{paramName:g,isOptional:b}=f;if(g==="*"){let y=a[d]||"";i=o.slice(0,o.length-y.length).replace(/(.)\/+$/,"$1")}const x=a[d];return b&&!x?c[g]=void 0:c[g]=(x||"").replace(/%2F/g,"/"),c},{}),pathname:o,pathnameBase:i,pattern:e}}function qh(e,t,r){t===void 0&&(t=!1),r===void 0&&(r=!0),wf(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let n=[],s="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(i,a,u)=>(n.push({paramName:a,isOptional:u!=null}),u?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(n.push({paramName:"*"}),s+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?s+="\\/*$":e!==""&&e!=="/"&&(s+="(?:(?=\\/|$))"),[new RegExp(s,t?void 0:"i"),n]}function Kh(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return wf(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function ya(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let r=t.endsWith("/")?t.length-1:t.length,n=e.charAt(r);return n&&n!=="/"?null:e.slice(r)||"/"}function Qh(e,t){t===void 0&&(t="/");let{pathname:r,search:n="",hash:s=""}=typeof e=="string"?rn(e):e;return{pathname:r?r.startsWith("/")?r:Jh(r,t):t,search:Xh(n),hash:Zh(s)}}function Jh(e,t){let r=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(s=>{s===".."?r.length>1&&r.pop():s!=="."&&r.push(s)}),r.length>1?r.join("/"):"/"}function xl(e,t,r,n){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(n)+"].  Please separate it out to the ")+("`to."+r+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function Yh(e){return e.filter((t,r)=>r===0||t.route.path&&t.route.path.length>0)}function va(e,t){let r=Yh(e);return t?r.map((n,s)=>s===r.length-1?n.pathname:n.pathnameBase):r.map(n=>n.pathnameBase)}function wa(e,t,r,n){n===void 0&&(n=!1);let s;typeof e=="string"?s=rn(e):(s=Jn({},e),ie(!s.pathname||!s.pathname.includes("?"),xl("?","pathname","search",s)),ie(!s.pathname||!s.pathname.includes("#"),xl("#","pathname","hash",s)),ie(!s.search||!s.search.includes("#"),xl("#","search","hash",s)));let o=e===""||s.pathname==="",i=o?"/":s.pathname,a;if(i==null)a=r;else{let d=t.length-1;if(!n&&i.startsWith("..")){let g=i.split("/");for(;g[0]==="..";)g.shift(),d-=1;s.pathname=g.join("/")}a=d>=0?t[d]:"/"}let u=Qh(s,a),c=i&&i!=="/"&&i.endsWith("/"),f=(o||i===".")&&r.endsWith("/");return!u.pathname.endsWith("/")&&(c||f)&&(u.pathname+="/"),u}const Xt=e=>e.join("/").replace(/\/\/+/g,"/"),Gh=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Xh=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Zh=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function e0(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const bf=["post","put","patch","delete"];new Set(bf);const t0=["get",...bf];new Set(t0);/**
 * React Router v6.30.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Yn(){return Yn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Yn.apply(this,arguments)}const Na=j.createContext(null),r0=j.createContext(null),sr=j.createContext(null),Mo=j.createContext(null),or=j.createContext({outlet:null,matches:[],isDataRoute:!1}),Sf=j.createContext(null);function n0(e,t){let{relative:r}=t===void 0?{}:t;nn()||ie(!1);let{basename:n,navigator:s}=j.useContext(sr),{hash:o,pathname:i,search:a}=kf(e,{relative:r}),u=i;return n!=="/"&&(u=i==="/"?n:Xt([n,i])),s.createHref({pathname:u,search:a,hash:o})}function nn(){return j.useContext(Mo)!=null}function Cr(){return nn()||ie(!1),j.useContext(Mo).location}function Cf(e){j.useContext(sr).static||j.useLayoutEffect(e)}function ns(){let{isDataRoute:e}=j.useContext(or);return e?g0():s0()}function s0(){nn()||ie(!1);let e=j.useContext(Na),{basename:t,future:r,navigator:n}=j.useContext(sr),{matches:s}=j.useContext(or),{pathname:o}=Cr(),i=JSON.stringify(va(s,r.v7_relativeSplatPath)),a=j.useRef(!1);return Cf(()=>{a.current=!0}),j.useCallback(function(c,f){if(f===void 0&&(f={}),!a.current)return;if(typeof c=="number"){n.go(c);return}let d=wa(c,JSON.parse(i),o,f.relative==="path");e==null&&t!=="/"&&(d.pathname=d.pathname==="/"?t:Xt([t,d.pathname])),(f.replace?n.replace:n.push)(d,f.state,f)},[t,n,i,o,e])}function kf(e,t){let{relative:r}=t===void 0?{}:t,{future:n}=j.useContext(sr),{matches:s}=j.useContext(or),{pathname:o}=Cr(),i=JSON.stringify(va(s,n.v7_relativeSplatPath));return j.useMemo(()=>wa(e,JSON.parse(i),o,r==="path"),[e,i,o,r])}function o0(e,t){return l0(e,t)}function l0(e,t,r,n){nn()||ie(!1);let{navigator:s,static:o}=j.useContext(sr),{matches:i}=j.useContext(or),a=i[i.length-1],u=a?a.params:{};a&&a.pathname;let c=a?a.pathnameBase:"/";a&&a.route;let f=Cr(),d;if(t){var g;let m=typeof t=="string"?rn(t):t;c==="/"||(g=m.pathname)!=null&&g.startsWith(c)||ie(!1),d=m}else d=f;let b=d.pathname||"/",x=b;if(c!=="/"){let m=c.replace(/^\//,"").split("/");x="/"+b.replace(/^\//,"").split("/").slice(m.length).join("/")}let y=!o&&r&&r.matches&&r.matches.length>0?r.matches:Mh(e,{pathname:x}),S=d0(y&&y.map(m=>Object.assign({},m,{params:Object.assign({},u,m.params),pathname:Xt([c,s.encodeLocation?s.encodeLocation(m.pathname).pathname:m.pathname]),pathnameBase:m.pathnameBase==="/"?c:Xt([c,s.encodeLocation?s.encodeLocation(m.pathnameBase).pathname:m.pathnameBase])})),i,r,n);return t&&S?j.createElement(Mo.Provider,{value:{location:Yn({pathname:"/",search:"",hash:"",state:null,key:"default"},d),navigationType:Ht.Pop}},S):S}function i0(){let e=h0(),t=e0(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),r=e instanceof Error?e.stack:null,s={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"},o=null;return j.createElement(j.Fragment,null,j.createElement("h2",null,"Unexpected Application Error!"),j.createElement("h3",{style:{fontStyle:"italic"}},t),r?j.createElement("pre",{style:s},r):null,o)}const a0=j.createElement(i0,null);class u0 extends j.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,r){return r.location!==t.location||r.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:r.error,location:r.location,revalidation:t.revalidation||r.revalidation}}componentDidCatch(t,r){console.error("React Router caught the following error during render",t,r)}render(){return this.state.error!==void 0?j.createElement(or.Provider,{value:this.props.routeContext},j.createElement(Sf.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function c0(e){let{routeContext:t,match:r,children:n}=e,s=j.useContext(Na);return s&&s.static&&s.staticContext&&(r.route.errorElement||r.route.ErrorBoundary)&&(s.staticContext._deepestRenderedBoundaryId=r.route.id),j.createElement(or.Provider,{value:t},n)}function d0(e,t,r,n){var s;if(t===void 0&&(t=[]),r===void 0&&(r=null),n===void 0&&(n=null),e==null){var o;if(!r)return null;if(r.errors)e=r.matches;else if((o=n)!=null&&o.v7_partialHydration&&t.length===0&&!r.initialized&&r.matches.length>0)e=r.matches;else return null}let i=e,a=(s=r)==null?void 0:s.errors;if(a!=null){let f=i.findIndex(d=>d.route.id&&(a==null?void 0:a[d.route.id])!==void 0);f>=0||ie(!1),i=i.slice(0,Math.min(i.length,f+1))}let u=!1,c=-1;if(r&&n&&n.v7_partialHydration)for(let f=0;f<i.length;f++){let d=i[f];if((d.route.HydrateFallback||d.route.hydrateFallbackElement)&&(c=f),d.route.id){let{loaderData:g,errors:b}=r,x=d.route.loader&&g[d.route.id]===void 0&&(!b||b[d.route.id]===void 0);if(d.route.lazy||x){u=!0,c>=0?i=i.slice(0,c+1):i=[i[0]];break}}}return i.reduceRight((f,d,g)=>{let b,x=!1,y=null,S=null;r&&(b=a&&d.route.id?a[d.route.id]:void 0,y=d.route.errorElement||a0,u&&(c<0&&g===0?(x0("route-fallback",!1),x=!0,S=null):c===g&&(x=!0,S=d.route.hydrateFallbackElement||null)));let m=t.concat(i.slice(0,g+1)),p=()=>{let h;return b?h=y:x?h=S:d.route.Component?h=j.createElement(d.route.Component,null):d.route.element?h=d.route.element:h=f,j.createElement(c0,{match:d,routeContext:{outlet:f,matches:m,isDataRoute:r!=null},children:h})};return r&&(d.route.ErrorBoundary||d.route.errorElement||g===0)?j.createElement(u0,{location:r.location,revalidation:r.revalidation,component:y,error:b,children:p(),routeContext:{outlet:null,matches:m,isDataRoute:!0}}):p()},null)}var _f=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(_f||{}),mo=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(mo||{});function f0(e){let t=j.useContext(Na);return t||ie(!1),t}function p0(e){let t=j.useContext(r0);return t||ie(!1),t}function m0(e){let t=j.useContext(or);return t||ie(!1),t}function Ef(e){let t=m0(),r=t.matches[t.matches.length-1];return r.route.id||ie(!1),r.route.id}function h0(){var e;let t=j.useContext(Sf),r=p0(mo.UseRouteError),n=Ef(mo.UseRouteError);return t!==void 0?t:(e=r.errors)==null?void 0:e[n]}function g0(){let{router:e}=f0(_f.UseNavigateStable),t=Ef(mo.UseNavigateStable),r=j.useRef(!1);return Cf(()=>{r.current=!0}),j.useCallback(function(s,o){o===void 0&&(o={}),r.current&&(typeof s=="number"?e.navigate(s):e.navigate(s,Yn({fromRouteId:t},o)))},[e,t])}const Bu={};function x0(e,t,r){!t&&!Bu[e]&&(Bu[e]=!0)}function y0(e,t){e==null||e.v7_startTransition,(e==null?void 0:e.v7_relativeSplatPath)===void 0&&(!t||t.v7_relativeSplatPath),t&&(t.v7_fetcherPersist,t.v7_normalizeFormMethod,t.v7_partialHydration,t.v7_skipActionErrorRevalidation)}function hi(e){let{to:t,replace:r,state:n,relative:s}=e;nn()||ie(!1);let{future:o,static:i}=j.useContext(sr),{matches:a}=j.useContext(or),{pathname:u}=Cr(),c=ns(),f=wa(t,va(a,o.v7_relativeSplatPath),u,s==="path"),d=JSON.stringify(f);return j.useEffect(()=>c(JSON.parse(d),{replace:r,state:n,relative:s}),[c,d,s,r,n]),null}function Us(e){ie(!1)}function v0(e){let{basename:t="/",children:r=null,location:n,navigationType:s=Ht.Pop,navigator:o,static:i=!1,future:a}=e;nn()&&ie(!1);let u=t.replace(/^\/*/,"/"),c=j.useMemo(()=>({basename:u,navigator:o,static:i,future:Yn({v7_relativeSplatPath:!1},a)}),[u,a,o,i]);typeof n=="string"&&(n=rn(n));let{pathname:f="/",search:d="",hash:g="",state:b=null,key:x="default"}=n,y=j.useMemo(()=>{let S=ya(f,u);return S==null?null:{location:{pathname:S,search:d,hash:g,state:b,key:x},navigationType:s}},[u,f,d,g,b,x,s]);return y==null?null:j.createElement(sr.Provider,{value:c},j.createElement(Mo.Provider,{children:r,value:y}))}function w0(e){let{children:t,location:r}=e;return o0(gi(t),r)}new Promise(()=>{});function gi(e,t){t===void 0&&(t=[]);let r=[];return j.Children.forEach(e,(n,s)=>{if(!j.isValidElement(n))return;let o=[...t,s];if(n.type===j.Fragment){r.push.apply(r,gi(n.props.children,o));return}n.type!==Us&&ie(!1),!n.props.index||!n.props.children||ie(!1);let i={id:n.props.id||o.join("-"),caseSensitive:n.props.caseSensitive,element:n.props.element,Component:n.props.Component,index:n.props.index,path:n.props.path,loader:n.props.loader,action:n.props.action,errorElement:n.props.errorElement,ErrorBoundary:n.props.ErrorBoundary,hasErrorBoundary:n.props.ErrorBoundary!=null||n.props.errorElement!=null,shouldRevalidate:n.props.shouldRevalidate,handle:n.props.handle,lazy:n.props.lazy};n.props.children&&(i.children=gi(n.props.children,o)),r.push(i)}),r}/**
 * React Router DOM v6.30.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function xi(){return xi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},xi.apply(this,arguments)}function N0(e,t){if(e==null)return{};var r={},n=Object.keys(e),s,o;for(o=0;o<n.length;o++)s=n[o],!(t.indexOf(s)>=0)&&(r[s]=e[s]);return r}function j0(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function b0(e,t){return e.button===0&&(!t||t==="_self")&&!j0(e)}const S0=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],C0="6";try{window.__reactRouterVersion=C0}catch{}const k0="startTransition",Hu=vp[k0];function _0(e){let{basename:t,children:r,future:n,window:s}=e,o=j.useRef();o.current==null&&(o.current=Th({window:s,v5Compat:!0}));let i=o.current,[a,u]=j.useState({action:i.action,location:i.location}),{v7_startTransition:c}=n||{},f=j.useCallback(d=>{c&&Hu?Hu(()=>u(d)):u(d)},[u,c]);return j.useLayoutEffect(()=>i.listen(f),[i,f]),j.useEffect(()=>y0(n),[n]),j.createElement(v0,{basename:t,children:r,location:a.location,navigationType:a.action,navigator:i,future:n})}const E0=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",P0=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Wu=j.forwardRef(function(t,r){let{onClick:n,relative:s,reloadDocument:o,replace:i,state:a,target:u,to:c,preventScrollReset:f,viewTransition:d}=t,g=N0(t,S0),{basename:b}=j.useContext(sr),x,y=!1;if(typeof c=="string"&&P0.test(c)&&(x=c,E0))try{let h=new URL(window.location.href),N=c.startsWith("//")?new URL(h.protocol+c):new URL(c),w=ya(N.pathname,b);N.origin===h.origin&&w!=null?c=w+N.search+N.hash:y=!0}catch{}let S=n0(c,{relative:s}),m=R0(c,{replace:i,state:a,target:u,preventScrollReset:f,relative:s,viewTransition:d});function p(h){n&&n(h),h.defaultPrevented||m(h)}return j.createElement("a",xi({},g,{href:x||S,onClick:y||o?n:p,ref:r,target:u}))});var Vu;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(Vu||(Vu={}));var qu;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(qu||(qu={}));function R0(e,t){let{target:r,replace:n,state:s,preventScrollReset:o,relative:i,viewTransition:a}=t===void 0?{}:t,u=ns(),c=Cr(),f=kf(e,{relative:i});return j.useCallback(d=>{if(b0(d,r)){d.preventDefault();let g=n!==void 0?n:po(c)===po(f);u(e,{replace:g,state:s,preventScrollReset:o,relative:i,viewTransition:a})}},[c,u,f,n,s,r,e,o,i,a])}function Pf(e,t){return function(){return e.apply(t,arguments)}}const{toString:T0}=Object.prototype,{getPrototypeOf:ja}=Object,{iterator:Fo,toStringTag:Rf}=Symbol,Ao=(e=>t=>{const r=T0.call(t);return e[r]||(e[r]=r.slice(8,-1).toLowerCase())})(Object.create(null)),ft=e=>(e=e.toLowerCase(),t=>Ao(t)===e),Uo=e=>t=>typeof t===e,{isArray:sn}=Array,Gn=Uo("undefined");function L0(e){return e!==null&&!Gn(e)&&e.constructor!==null&&!Gn(e.constructor)&&Be(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const Tf=ft("ArrayBuffer");function O0(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&Tf(e.buffer),t}const M0=Uo("string"),Be=Uo("function"),Lf=Uo("number"),zo=e=>e!==null&&typeof e=="object",F0=e=>e===!0||e===!1,zs=e=>{if(Ao(e)!=="object")return!1;const t=ja(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Rf in e)&&!(Fo in e)},A0=ft("Date"),U0=ft("File"),z0=ft("Blob"),D0=ft("FileList"),I0=e=>zo(e)&&Be(e.pipe),$0=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||Be(e.append)&&((t=Ao(e))==="formdata"||t==="object"&&Be(e.toString)&&e.toString()==="[object FormData]"))},B0=ft("URLSearchParams"),[H0,W0,V0,q0]=["ReadableStream","Request","Response","Headers"].map(ft),K0=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function ss(e,t,{allOwnKeys:r=!1}={}){if(e===null||typeof e>"u")return;let n,s;if(typeof e!="object"&&(e=[e]),sn(e))for(n=0,s=e.length;n<s;n++)t.call(null,e[n],n,e);else{const o=r?Object.getOwnPropertyNames(e):Object.keys(e),i=o.length;let a;for(n=0;n<i;n++)a=o[n],t.call(null,e[a],a,e)}}function Of(e,t){t=t.toLowerCase();const r=Object.keys(e);let n=r.length,s;for(;n-- >0;)if(s=r[n],t===s.toLowerCase())return s;return null}const pr=(()=>typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global)(),Mf=e=>!Gn(e)&&e!==pr;function yi(){const{caseless:e}=Mf(this)&&this||{},t={},r=(n,s)=>{const o=e&&Of(t,s)||s;zs(t[o])&&zs(n)?t[o]=yi(t[o],n):zs(n)?t[o]=yi({},n):sn(n)?t[o]=n.slice():t[o]=n};for(let n=0,s=arguments.length;n<s;n++)arguments[n]&&ss(arguments[n],r);return t}const Q0=(e,t,r,{allOwnKeys:n}={})=>(ss(t,(s,o)=>{r&&Be(s)?e[o]=Pf(s,r):e[o]=s},{allOwnKeys:n}),e),J0=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),Y0=(e,t,r,n)=>{e.prototype=Object.create(t.prototype,n),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),r&&Object.assign(e.prototype,r)},G0=(e,t,r,n)=>{let s,o,i;const a={};if(t=t||{},e==null)return t;do{for(s=Object.getOwnPropertyNames(e),o=s.length;o-- >0;)i=s[o],(!n||n(i,e,t))&&!a[i]&&(t[i]=e[i],a[i]=!0);e=r!==!1&&ja(e)}while(e&&(!r||r(e,t))&&e!==Object.prototype);return t},X0=(e,t,r)=>{e=String(e),(r===void 0||r>e.length)&&(r=e.length),r-=t.length;const n=e.indexOf(t,r);return n!==-1&&n===r},Z0=e=>{if(!e)return null;if(sn(e))return e;let t=e.length;if(!Lf(t))return null;const r=new Array(t);for(;t-- >0;)r[t]=e[t];return r},eg=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&ja(Uint8Array)),tg=(e,t)=>{const n=(e&&e[Fo]).call(e);let s;for(;(s=n.next())&&!s.done;){const o=s.value;t.call(e,o[0],o[1])}},rg=(e,t)=>{let r;const n=[];for(;(r=e.exec(t))!==null;)n.push(r);return n},ng=ft("HTMLFormElement"),sg=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(r,n,s){return n.toUpperCase()+s}),Ku=(({hasOwnProperty:e})=>(t,r)=>e.call(t,r))(Object.prototype),og=ft("RegExp"),Ff=(e,t)=>{const r=Object.getOwnPropertyDescriptors(e),n={};ss(r,(s,o)=>{let i;(i=t(s,o,e))!==!1&&(n[o]=i||s)}),Object.defineProperties(e,n)},lg=e=>{Ff(e,(t,r)=>{if(Be(e)&&["arguments","caller","callee"].indexOf(r)!==-1)return!1;const n=e[r];if(Be(n)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+r+"'")})}})},ig=(e,t)=>{const r={},n=s=>{s.forEach(o=>{r[o]=!0})};return sn(e)?n(e):n(String(e).split(t)),r},ag=()=>{},ug=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function cg(e){return!!(e&&Be(e.append)&&e[Rf]==="FormData"&&e[Fo])}const dg=e=>{const t=new Array(10),r=(n,s)=>{if(zo(n)){if(t.indexOf(n)>=0)return;if(!("toJSON"in n)){t[s]=n;const o=sn(n)?[]:{};return ss(n,(i,a)=>{const u=r(i,s+1);!Gn(u)&&(o[a]=u)}),t[s]=void 0,o}}return n};return r(e,0)},fg=ft("AsyncFunction"),pg=e=>e&&(zo(e)||Be(e))&&Be(e.then)&&Be(e.catch),Af=((e,t)=>e?setImmediate:t?((r,n)=>(pr.addEventListener("message",({source:s,data:o})=>{s===pr&&o===r&&n.length&&n.shift()()},!1),s=>{n.push(s),pr.postMessage(r,"*")}))(`axios@${Math.random()}`,[]):r=>setTimeout(r))(typeof setImmediate=="function",Be(pr.postMessage)),mg=typeof queueMicrotask<"u"?queueMicrotask.bind(pr):typeof process<"u"&&process.nextTick||Af,hg=e=>e!=null&&Be(e[Fo]),C={isArray:sn,isArrayBuffer:Tf,isBuffer:L0,isFormData:$0,isArrayBufferView:O0,isString:M0,isNumber:Lf,isBoolean:F0,isObject:zo,isPlainObject:zs,isReadableStream:H0,isRequest:W0,isResponse:V0,isHeaders:q0,isUndefined:Gn,isDate:A0,isFile:U0,isBlob:z0,isRegExp:og,isFunction:Be,isStream:I0,isURLSearchParams:B0,isTypedArray:eg,isFileList:D0,forEach:ss,merge:yi,extend:Q0,trim:K0,stripBOM:J0,inherits:Y0,toFlatObject:G0,kindOf:Ao,kindOfTest:ft,endsWith:X0,toArray:Z0,forEachEntry:tg,matchAll:rg,isHTMLForm:ng,hasOwnProperty:Ku,hasOwnProp:Ku,reduceDescriptors:Ff,freezeMethods:lg,toObjectSet:ig,toCamelCase:sg,noop:ag,toFiniteNumber:ug,findKey:Of,global:pr,isContextDefined:Mf,isSpecCompliantForm:cg,toJSONObject:dg,isAsyncFn:fg,isThenable:pg,setImmediate:Af,asap:mg,isIterable:hg};function z(e,t,r,n,s){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),r&&(this.config=r),n&&(this.request=n),s&&(this.response=s,this.status=s.status?s.status:null)}C.inherits(z,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:C.toJSONObject(this.config),code:this.code,status:this.status}}});const Uf=z.prototype,zf={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{zf[e]={value:e}});Object.defineProperties(z,zf);Object.defineProperty(Uf,"isAxiosError",{value:!0});z.from=(e,t,r,n,s,o)=>{const i=Object.create(Uf);return C.toFlatObject(e,i,function(u){return u!==Error.prototype},a=>a!=="isAxiosError"),z.call(i,e.message,t,r,n,s),i.cause=e,i.name=e.name,o&&Object.assign(i,o),i};const gg=null;function vi(e){return C.isPlainObject(e)||C.isArray(e)}function Df(e){return C.endsWith(e,"[]")?e.slice(0,-2):e}function Qu(e,t,r){return e?e.concat(t).map(function(s,o){return s=Df(s),!r&&o?"["+s+"]":s}).join(r?".":""):t}function xg(e){return C.isArray(e)&&!e.some(vi)}const yg=C.toFlatObject(C,{},null,function(t){return/^is[A-Z]/.test(t)});function Do(e,t,r){if(!C.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,r=C.toFlatObject(r,{metaTokens:!0,dots:!1,indexes:!1},!1,function(y,S){return!C.isUndefined(S[y])});const n=r.metaTokens,s=r.visitor||f,o=r.dots,i=r.indexes,u=(r.Blob||typeof Blob<"u"&&Blob)&&C.isSpecCompliantForm(t);if(!C.isFunction(s))throw new TypeError("visitor must be a function");function c(x){if(x===null)return"";if(C.isDate(x))return x.toISOString();if(!u&&C.isBlob(x))throw new z("Blob is not supported. Use a Buffer instead.");return C.isArrayBuffer(x)||C.isTypedArray(x)?u&&typeof Blob=="function"?new Blob([x]):Buffer.from(x):x}function f(x,y,S){let m=x;if(x&&!S&&typeof x=="object"){if(C.endsWith(y,"{}"))y=n?y:y.slice(0,-2),x=JSON.stringify(x);else if(C.isArray(x)&&xg(x)||(C.isFileList(x)||C.endsWith(y,"[]"))&&(m=C.toArray(x)))return y=Df(y),m.forEach(function(h,N){!(C.isUndefined(h)||h===null)&&t.append(i===!0?Qu([y],N,o):i===null?y:y+"[]",c(h))}),!1}return vi(x)?!0:(t.append(Qu(S,y,o),c(x)),!1)}const d=[],g=Object.assign(yg,{defaultVisitor:f,convertValue:c,isVisitable:vi});function b(x,y){if(!C.isUndefined(x)){if(d.indexOf(x)!==-1)throw Error("Circular reference detected in "+y.join("."));d.push(x),C.forEach(x,function(m,p){(!(C.isUndefined(m)||m===null)&&s.call(t,m,C.isString(p)?p.trim():p,y,g))===!0&&b(m,y?y.concat(p):[p])}),d.pop()}}if(!C.isObject(e))throw new TypeError("data must be an object");return b(e),t}function Ju(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(n){return t[n]})}function ba(e,t){this._pairs=[],e&&Do(e,this,t)}const If=ba.prototype;If.append=function(t,r){this._pairs.push([t,r])};If.toString=function(t){const r=t?function(n){return t.call(this,n,Ju)}:Ju;return this._pairs.map(function(s){return r(s[0])+"="+r(s[1])},"").join("&")};function vg(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function $f(e,t,r){if(!t)return e;const n=r&&r.encode||vg;C.isFunction(r)&&(r={serialize:r});const s=r&&r.serialize;let o;if(s?o=s(t,r):o=C.isURLSearchParams(t)?t.toString():new ba(t,r).toString(n),o){const i=e.indexOf("#");i!==-1&&(e=e.slice(0,i)),e+=(e.indexOf("?")===-1?"?":"&")+o}return e}class wg{constructor(){this.handlers=[]}use(t,r,n){return this.handlers.push({fulfilled:t,rejected:r,synchronous:n?n.synchronous:!1,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){C.forEach(this.handlers,function(n){n!==null&&t(n)})}}const Yu=wg,Bf={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Ng=typeof URLSearchParams<"u"?URLSearchParams:ba,jg=typeof FormData<"u"?FormData:null,bg=typeof Blob<"u"?Blob:null,Sg={isBrowser:!0,classes:{URLSearchParams:Ng,FormData:jg,Blob:bg},protocols:["http","https","file","blob","url","data"]},Sa=typeof window<"u"&&typeof document<"u",wi=typeof navigator=="object"&&navigator||void 0,Cg=Sa&&(!wi||["ReactNative","NativeScript","NS"].indexOf(wi.product)<0),kg=(()=>typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function")(),_g=Sa&&window.location.href||"http://localhost",Eg=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Sa,hasStandardBrowserEnv:Cg,hasStandardBrowserWebWorkerEnv:kg,navigator:wi,origin:_g},Symbol.toStringTag,{value:"Module"})),Ee={...Eg,...Sg};function Pg(e,t){return Do(e,new Ee.classes.URLSearchParams,Object.assign({visitor:function(r,n,s,o){return Ee.isNode&&C.isBuffer(r)?(this.append(n,r.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)}},t))}function Rg(e){return C.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function Tg(e){const t={},r=Object.keys(e);let n;const s=r.length;let o;for(n=0;n<s;n++)o=r[n],t[o]=e[o];return t}function Hf(e){function t(r,n,s,o){let i=r[o++];if(i==="__proto__")return!0;const a=Number.isFinite(+i),u=o>=r.length;return i=!i&&C.isArray(s)?s.length:i,u?(C.hasOwnProp(s,i)?s[i]=[s[i],n]:s[i]=n,!a):((!s[i]||!C.isObject(s[i]))&&(s[i]=[]),t(r,n,s[i],o)&&C.isArray(s[i])&&(s[i]=Tg(s[i])),!a)}if(C.isFormData(e)&&C.isFunction(e.entries)){const r={};return C.forEachEntry(e,(n,s)=>{t(Rg(n),s,r,0)}),r}return null}function Lg(e,t,r){if(C.isString(e))try{return(t||JSON.parse)(e),C.trim(e)}catch(n){if(n.name!=="SyntaxError")throw n}return(r||JSON.stringify)(e)}const Ca={transitional:Bf,adapter:["xhr","http","fetch"],transformRequest:[function(t,r){const n=r.getContentType()||"",s=n.indexOf("application/json")>-1,o=C.isObject(t);if(o&&C.isHTMLForm(t)&&(t=new FormData(t)),C.isFormData(t))return s?JSON.stringify(Hf(t)):t;if(C.isArrayBuffer(t)||C.isBuffer(t)||C.isStream(t)||C.isFile(t)||C.isBlob(t)||C.isReadableStream(t))return t;if(C.isArrayBufferView(t))return t.buffer;if(C.isURLSearchParams(t))return r.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let a;if(o){if(n.indexOf("application/x-www-form-urlencoded")>-1)return Pg(t,this.formSerializer).toString();if((a=C.isFileList(t))||n.indexOf("multipart/form-data")>-1){const u=this.env&&this.env.FormData;return Do(a?{"files[]":t}:t,u&&new u,this.formSerializer)}}return o||s?(r.setContentType("application/json",!1),Lg(t)):t}],transformResponse:[function(t){const r=this.transitional||Ca.transitional,n=r&&r.forcedJSONParsing,s=this.responseType==="json";if(C.isResponse(t)||C.isReadableStream(t))return t;if(t&&C.isString(t)&&(n&&!this.responseType||s)){const i=!(r&&r.silentJSONParsing)&&s;try{return JSON.parse(t)}catch(a){if(i)throw a.name==="SyntaxError"?z.from(a,z.ERR_BAD_RESPONSE,this,null,this.response):a}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Ee.classes.FormData,Blob:Ee.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};C.forEach(["delete","get","head","post","put","patch"],e=>{Ca.headers[e]={}});const ka=Ca,Og=C.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Mg=e=>{const t={};let r,n,s;return e&&e.split(`
`).forEach(function(i){s=i.indexOf(":"),r=i.substring(0,s).trim().toLowerCase(),n=i.substring(s+1).trim(),!(!r||t[r]&&Og[r])&&(r==="set-cookie"?t[r]?t[r].push(n):t[r]=[n]:t[r]=t[r]?t[r]+", "+n:n)}),t},Gu=Symbol("internals");function xn(e){return e&&String(e).trim().toLowerCase()}function Ds(e){return e===!1||e==null?e:C.isArray(e)?e.map(Ds):String(e)}function Fg(e){const t=Object.create(null),r=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let n;for(;n=r.exec(e);)t[n[1]]=n[2];return t}const Ag=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function yl(e,t,r,n,s){if(C.isFunction(n))return n.call(this,t,r);if(s&&(t=r),!!C.isString(t)){if(C.isString(n))return t.indexOf(n)!==-1;if(C.isRegExp(n))return n.test(t)}}function Ug(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,r,n)=>r.toUpperCase()+n)}function zg(e,t){const r=C.toCamelCase(" "+t);["get","set","has"].forEach(n=>{Object.defineProperty(e,n+r,{value:function(s,o,i){return this[n].call(this,t,s,o,i)},configurable:!0})})}class Io{constructor(t){t&&this.set(t)}set(t,r,n){const s=this;function o(a,u,c){const f=xn(u);if(!f)throw new Error("header name must be a non-empty string");const d=C.findKey(s,f);(!d||s[d]===void 0||c===!0||c===void 0&&s[d]!==!1)&&(s[d||u]=Ds(a))}const i=(a,u)=>C.forEach(a,(c,f)=>o(c,f,u));if(C.isPlainObject(t)||t instanceof this.constructor)i(t,r);else if(C.isString(t)&&(t=t.trim())&&!Ag(t))i(Mg(t),r);else if(C.isObject(t)&&C.isIterable(t)){let a={},u,c;for(const f of t){if(!C.isArray(f))throw TypeError("Object iterator must return a key-value pair");a[c=f[0]]=(u=a[c])?C.isArray(u)?[...u,f[1]]:[u,f[1]]:f[1]}i(a,r)}else t!=null&&o(r,t,n);return this}get(t,r){if(t=xn(t),t){const n=C.findKey(this,t);if(n){const s=this[n];if(!r)return s;if(r===!0)return Fg(s);if(C.isFunction(r))return r.call(this,s,n);if(C.isRegExp(r))return r.exec(s);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,r){if(t=xn(t),t){const n=C.findKey(this,t);return!!(n&&this[n]!==void 0&&(!r||yl(this,this[n],n,r)))}return!1}delete(t,r){const n=this;let s=!1;function o(i){if(i=xn(i),i){const a=C.findKey(n,i);a&&(!r||yl(n,n[a],a,r))&&(delete n[a],s=!0)}}return C.isArray(t)?t.forEach(o):o(t),s}clear(t){const r=Object.keys(this);let n=r.length,s=!1;for(;n--;){const o=r[n];(!t||yl(this,this[o],o,t,!0))&&(delete this[o],s=!0)}return s}normalize(t){const r=this,n={};return C.forEach(this,(s,o)=>{const i=C.findKey(n,o);if(i){r[i]=Ds(s),delete r[o];return}const a=t?Ug(o):String(o).trim();a!==o&&delete r[o],r[a]=Ds(s),n[a]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const r=Object.create(null);return C.forEach(this,(n,s)=>{n!=null&&n!==!1&&(r[s]=t&&C.isArray(n)?n.join(", "):n)}),r}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,r])=>t+": "+r).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...r){const n=new this(t);return r.forEach(s=>n.set(s)),n}static accessor(t){const n=(this[Gu]=this[Gu]={accessors:{}}).accessors,s=this.prototype;function o(i){const a=xn(i);n[a]||(zg(s,i),n[a]=!0)}return C.isArray(t)?t.forEach(o):o(t),this}}Io.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);C.reduceDescriptors(Io.prototype,({value:e},t)=>{let r=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(n){this[r]=n}}});C.freezeMethods(Io);const ut=Io;function vl(e,t){const r=this||ka,n=t||r,s=ut.from(n.headers);let o=n.data;return C.forEach(e,function(a){o=a.call(r,o,s.normalize(),t?t.status:void 0)}),s.normalize(),o}function Wf(e){return!!(e&&e.__CANCEL__)}function on(e,t,r){z.call(this,e??"canceled",z.ERR_CANCELED,t,r),this.name="CanceledError"}C.inherits(on,z,{__CANCEL__:!0});function Vf(e,t,r){const n=r.config.validateStatus;!r.status||!n||n(r.status)?e(r):t(new z("Request failed with status code "+r.status,[z.ERR_BAD_REQUEST,z.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r))}function Dg(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function Ig(e,t){e=e||10;const r=new Array(e),n=new Array(e);let s=0,o=0,i;return t=t!==void 0?t:1e3,function(u){const c=Date.now(),f=n[o];i||(i=c),r[s]=u,n[s]=c;let d=o,g=0;for(;d!==s;)g+=r[d++],d=d%e;if(s=(s+1)%e,s===o&&(o=(o+1)%e),c-i<t)return;const b=f&&c-f;return b?Math.round(g*1e3/b):void 0}}function $g(e,t){let r=0,n=1e3/t,s,o;const i=(c,f=Date.now())=>{r=f,s=null,o&&(clearTimeout(o),o=null),e.apply(null,c)};return[(...c)=>{const f=Date.now(),d=f-r;d>=n?i(c,f):(s=c,o||(o=setTimeout(()=>{o=null,i(s)},n-d)))},()=>s&&i(s)]}const ho=(e,t,r=3)=>{let n=0;const s=Ig(50,250);return $g(o=>{const i=o.loaded,a=o.lengthComputable?o.total:void 0,u=i-n,c=s(u),f=i<=a;n=i;const d={loaded:i,total:a,progress:a?i/a:void 0,bytes:u,rate:c||void 0,estimated:c&&a&&f?(a-i)/c:void 0,event:o,lengthComputable:a!=null,[t?"download":"upload"]:!0};e(d)},r)},Xu=(e,t)=>{const r=e!=null;return[n=>t[0]({lengthComputable:r,total:e,loaded:n}),t[1]]},Zu=e=>(...t)=>C.asap(()=>e(...t)),Bg=Ee.hasStandardBrowserEnv?((e,t)=>r=>(r=new URL(r,Ee.origin),e.protocol===r.protocol&&e.host===r.host&&(t||e.port===r.port)))(new URL(Ee.origin),Ee.navigator&&/(msie|trident)/i.test(Ee.navigator.userAgent)):()=>!0,Hg=Ee.hasStandardBrowserEnv?{write(e,t,r,n,s,o){const i=[e+"="+encodeURIComponent(t)];C.isNumber(r)&&i.push("expires="+new Date(r).toGMTString()),C.isString(n)&&i.push("path="+n),C.isString(s)&&i.push("domain="+s),o===!0&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Wg(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function Vg(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function qf(e,t,r){let n=!Wg(t);return e&&(n||r==!1)?Vg(e,t):t}const ec=e=>e instanceof ut?{...e}:e;function Nr(e,t){t=t||{};const r={};function n(c,f,d,g){return C.isPlainObject(c)&&C.isPlainObject(f)?C.merge.call({caseless:g},c,f):C.isPlainObject(f)?C.merge({},f):C.isArray(f)?f.slice():f}function s(c,f,d,g){if(C.isUndefined(f)){if(!C.isUndefined(c))return n(void 0,c,d,g)}else return n(c,f,d,g)}function o(c,f){if(!C.isUndefined(f))return n(void 0,f)}function i(c,f){if(C.isUndefined(f)){if(!C.isUndefined(c))return n(void 0,c)}else return n(void 0,f)}function a(c,f,d){if(d in t)return n(c,f);if(d in e)return n(void 0,c)}const u={url:o,method:o,data:o,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:a,headers:(c,f,d)=>s(ec(c),ec(f),d,!0)};return C.forEach(Object.keys(Object.assign({},e,t)),function(f){const d=u[f]||s,g=d(e[f],t[f],f);C.isUndefined(g)&&d!==a||(r[f]=g)}),r}const Kf=e=>{const t=Nr({},e);let{data:r,withXSRFToken:n,xsrfHeaderName:s,xsrfCookieName:o,headers:i,auth:a}=t;t.headers=i=ut.from(i),t.url=$f(qf(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),a&&i.set("Authorization","Basic "+btoa((a.username||"")+":"+(a.password?unescape(encodeURIComponent(a.password)):"")));let u;if(C.isFormData(r)){if(Ee.hasStandardBrowserEnv||Ee.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((u=i.getContentType())!==!1){const[c,...f]=u?u.split(";").map(d=>d.trim()).filter(Boolean):[];i.setContentType([c||"multipart/form-data",...f].join("; "))}}if(Ee.hasStandardBrowserEnv&&(n&&C.isFunction(n)&&(n=n(t)),n||n!==!1&&Bg(t.url))){const c=s&&o&&Hg.read(o);c&&i.set(s,c)}return t},qg=typeof XMLHttpRequest<"u",Kg=qg&&function(e){return new Promise(function(r,n){const s=Kf(e);let o=s.data;const i=ut.from(s.headers).normalize();let{responseType:a,onUploadProgress:u,onDownloadProgress:c}=s,f,d,g,b,x;function y(){b&&b(),x&&x(),s.cancelToken&&s.cancelToken.unsubscribe(f),s.signal&&s.signal.removeEventListener("abort",f)}let S=new XMLHttpRequest;S.open(s.method.toUpperCase(),s.url,!0),S.timeout=s.timeout;function m(){if(!S)return;const h=ut.from("getAllResponseHeaders"in S&&S.getAllResponseHeaders()),w={data:!a||a==="text"||a==="json"?S.responseText:S.response,status:S.status,statusText:S.statusText,headers:h,config:e,request:S};Vf(function(T){r(T),y()},function(T){n(T),y()},w),S=null}"onloadend"in S?S.onloadend=m:S.onreadystatechange=function(){!S||S.readyState!==4||S.status===0&&!(S.responseURL&&S.responseURL.indexOf("file:")===0)||setTimeout(m)},S.onabort=function(){S&&(n(new z("Request aborted",z.ECONNABORTED,e,S)),S=null)},S.onerror=function(){n(new z("Network Error",z.ERR_NETWORK,e,S)),S=null},S.ontimeout=function(){let N=s.timeout?"timeout of "+s.timeout+"ms exceeded":"timeout exceeded";const w=s.transitional||Bf;s.timeoutErrorMessage&&(N=s.timeoutErrorMessage),n(new z(N,w.clarifyTimeoutError?z.ETIMEDOUT:z.ECONNABORTED,e,S)),S=null},o===void 0&&i.setContentType(null),"setRequestHeader"in S&&C.forEach(i.toJSON(),function(N,w){S.setRequestHeader(w,N)}),C.isUndefined(s.withCredentials)||(S.withCredentials=!!s.withCredentials),a&&a!=="json"&&(S.responseType=s.responseType),c&&([g,x]=ho(c,!0),S.addEventListener("progress",g)),u&&S.upload&&([d,b]=ho(u),S.upload.addEventListener("progress",d),S.upload.addEventListener("loadend",b)),(s.cancelToken||s.signal)&&(f=h=>{S&&(n(!h||h.type?new on(null,e,S):h),S.abort(),S=null)},s.cancelToken&&s.cancelToken.subscribe(f),s.signal&&(s.signal.aborted?f():s.signal.addEventListener("abort",f)));const p=Dg(s.url);if(p&&Ee.protocols.indexOf(p)===-1){n(new z("Unsupported protocol "+p+":",z.ERR_BAD_REQUEST,e));return}S.send(o||null)})},Qg=(e,t)=>{const{length:r}=e=e?e.filter(Boolean):[];if(t||r){let n=new AbortController,s;const o=function(c){if(!s){s=!0,a();const f=c instanceof Error?c:this.reason;n.abort(f instanceof z?f:new on(f instanceof Error?f.message:f))}};let i=t&&setTimeout(()=>{i=null,o(new z(`timeout ${t} of ms exceeded`,z.ETIMEDOUT))},t);const a=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach(c=>{c.unsubscribe?c.unsubscribe(o):c.removeEventListener("abort",o)}),e=null)};e.forEach(c=>c.addEventListener("abort",o));const{signal:u}=n;return u.unsubscribe=()=>C.asap(a),u}},Jg=Qg,Yg=function*(e,t){let r=e.byteLength;if(!t||r<t){yield e;return}let n=0,s;for(;n<r;)s=n+t,yield e.slice(n,s),n=s},Gg=async function*(e,t){for await(const r of Xg(e))yield*Yg(r,t)},Xg=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:r,value:n}=await t.read();if(r)break;yield n}}finally{await t.cancel()}},tc=(e,t,r,n)=>{const s=Gg(e,t);let o=0,i,a=u=>{i||(i=!0,n&&n(u))};return new ReadableStream({async pull(u){try{const{done:c,value:f}=await s.next();if(c){a(),u.close();return}let d=f.byteLength;if(r){let g=o+=d;r(g)}u.enqueue(new Uint8Array(f))}catch(c){throw a(c),c}},cancel(u){return a(u),s.return()}},{highWaterMark:2})},$o=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Qf=$o&&typeof ReadableStream=="function",Zg=$o&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),Jf=(e,...t)=>{try{return!!e(...t)}catch{return!1}},ex=Qf&&Jf(()=>{let e=!1;const t=new Request(Ee.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),rc=64*1024,Ni=Qf&&Jf(()=>C.isReadableStream(new Response("").body)),go={stream:Ni&&(e=>e.body)};$o&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!go[t]&&(go[t]=C.isFunction(e[t])?r=>r[t]():(r,n)=>{throw new z(`Response type '${t}' is not supported`,z.ERR_NOT_SUPPORT,n)})})})(new Response);const tx=async e=>{if(e==null)return 0;if(C.isBlob(e))return e.size;if(C.isSpecCompliantForm(e))return(await new Request(Ee.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(C.isArrayBufferView(e)||C.isArrayBuffer(e))return e.byteLength;if(C.isURLSearchParams(e)&&(e=e+""),C.isString(e))return(await Zg(e)).byteLength},rx=async(e,t)=>{const r=C.toFiniteNumber(e.getContentLength());return r??tx(t)},nx=$o&&(async e=>{let{url:t,method:r,data:n,signal:s,cancelToken:o,timeout:i,onDownloadProgress:a,onUploadProgress:u,responseType:c,headers:f,withCredentials:d="same-origin",fetchOptions:g}=Kf(e);c=c?(c+"").toLowerCase():"text";let b=Jg([s,o&&o.toAbortSignal()],i),x;const y=b&&b.unsubscribe&&(()=>{b.unsubscribe()});let S;try{if(u&&ex&&r!=="get"&&r!=="head"&&(S=await rx(f,n))!==0){let w=new Request(t,{method:"POST",body:n,duplex:"half"}),_;if(C.isFormData(n)&&(_=w.headers.get("content-type"))&&f.setContentType(_),w.body){const[T,R]=Xu(S,ho(Zu(u)));n=tc(w.body,rc,T,R)}}C.isString(d)||(d=d?"include":"omit");const m="credentials"in Request.prototype;x=new Request(t,{...g,signal:b,method:r.toUpperCase(),headers:f.normalize().toJSON(),body:n,duplex:"half",credentials:m?d:void 0});let p=await fetch(x);const h=Ni&&(c==="stream"||c==="response");if(Ni&&(a||h&&y)){const w={};["status","statusText","headers"].forEach(D=>{w[D]=p[D]});const _=C.toFiniteNumber(p.headers.get("content-length")),[T,R]=a&&Xu(_,ho(Zu(a),!0))||[];p=new Response(tc(p.body,rc,T,()=>{R&&R(),y&&y()}),w)}c=c||"text";let N=await go[C.findKey(go,c)||"text"](p,e);return!h&&y&&y(),await new Promise((w,_)=>{Vf(w,_,{data:N,headers:ut.from(p.headers),status:p.status,statusText:p.statusText,config:e,request:x})})}catch(m){throw y&&y(),m&&m.name==="TypeError"&&/Load failed|fetch/i.test(m.message)?Object.assign(new z("Network Error",z.ERR_NETWORK,e,x),{cause:m.cause||m}):z.from(m,m&&m.code,e,x)}}),ji={http:gg,xhr:Kg,fetch:nx};C.forEach(ji,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const nc=e=>`- ${e}`,sx=e=>C.isFunction(e)||e===null||e===!1,Yf={getAdapter:e=>{e=C.isArray(e)?e:[e];const{length:t}=e;let r,n;const s={};for(let o=0;o<t;o++){r=e[o];let i;if(n=r,!sx(r)&&(n=ji[(i=String(r)).toLowerCase()],n===void 0))throw new z(`Unknown adapter '${i}'`);if(n)break;s[i||"#"+o]=n}if(!n){const o=Object.entries(s).map(([a,u])=>`adapter ${a} `+(u===!1?"is not supported by the environment":"is not available in the build"));let i=t?o.length>1?`since :
`+o.map(nc).join(`
`):" "+nc(o[0]):"as no adapter specified";throw new z("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return n},adapters:ji};function wl(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new on(null,e)}function sc(e){return wl(e),e.headers=ut.from(e.headers),e.data=vl.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),Yf.getAdapter(e.adapter||ka.adapter)(e).then(function(n){return wl(e),n.data=vl.call(e,e.transformResponse,n),n.headers=ut.from(n.headers),n},function(n){return Wf(n)||(wl(e),n&&n.response&&(n.response.data=vl.call(e,e.transformResponse,n.response),n.response.headers=ut.from(n.response.headers))),Promise.reject(n)})}const Gf="1.9.0",Bo={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{Bo[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}});const oc={};Bo.transitional=function(t,r,n){function s(o,i){return"[Axios v"+Gf+"] Transitional option '"+o+"'"+i+(n?". "+n:"")}return(o,i,a)=>{if(t===!1)throw new z(s(i," has been removed"+(r?" in "+r:"")),z.ERR_DEPRECATED);return r&&!oc[i]&&(oc[i]=!0,console.warn(s(i," has been deprecated since v"+r+" and will be removed in the near future"))),t?t(o,i,a):!0}};Bo.spelling=function(t){return(r,n)=>(console.warn(`${n} is likely a misspelling of ${t}`),!0)};function ox(e,t,r){if(typeof e!="object")throw new z("options must be an object",z.ERR_BAD_OPTION_VALUE);const n=Object.keys(e);let s=n.length;for(;s-- >0;){const o=n[s],i=t[o];if(i){const a=e[o],u=a===void 0||i(a,o,e);if(u!==!0)throw new z("option "+o+" must be "+u,z.ERR_BAD_OPTION_VALUE);continue}if(r!==!0)throw new z("Unknown option "+o,z.ERR_BAD_OPTION)}}const Is={assertOptions:ox,validators:Bo},gt=Is.validators;class xo{constructor(t){this.defaults=t||{},this.interceptors={request:new Yu,response:new Yu}}async request(t,r){try{return await this._request(t,r)}catch(n){if(n instanceof Error){let s={};Error.captureStackTrace?Error.captureStackTrace(s):s=new Error;const o=s.stack?s.stack.replace(/^.+\n/,""):"";try{n.stack?o&&!String(n.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(n.stack+=`
`+o):n.stack=o}catch{}}throw n}}_request(t,r){typeof t=="string"?(r=r||{},r.url=t):r=t||{},r=Nr(this.defaults,r);const{transitional:n,paramsSerializer:s,headers:o}=r;n!==void 0&&Is.assertOptions(n,{silentJSONParsing:gt.transitional(gt.boolean),forcedJSONParsing:gt.transitional(gt.boolean),clarifyTimeoutError:gt.transitional(gt.boolean)},!1),s!=null&&(C.isFunction(s)?r.paramsSerializer={serialize:s}:Is.assertOptions(s,{encode:gt.function,serialize:gt.function},!0)),r.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?r.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:r.allowAbsoluteUrls=!0),Is.assertOptions(r,{baseUrl:gt.spelling("baseURL"),withXsrfToken:gt.spelling("withXSRFToken")},!0),r.method=(r.method||this.defaults.method||"get").toLowerCase();let i=o&&C.merge(o.common,o[r.method]);o&&C.forEach(["delete","get","head","post","put","patch","common"],x=>{delete o[x]}),r.headers=ut.concat(i,o);const a=[];let u=!0;this.interceptors.request.forEach(function(y){typeof y.runWhen=="function"&&y.runWhen(r)===!1||(u=u&&y.synchronous,a.unshift(y.fulfilled,y.rejected))});const c=[];this.interceptors.response.forEach(function(y){c.push(y.fulfilled,y.rejected)});let f,d=0,g;if(!u){const x=[sc.bind(this),void 0];for(x.unshift.apply(x,a),x.push.apply(x,c),g=x.length,f=Promise.resolve(r);d<g;)f=f.then(x[d++],x[d++]);return f}g=a.length;let b=r;for(d=0;d<g;){const x=a[d++],y=a[d++];try{b=x(b)}catch(S){y.call(this,S);break}}try{f=sc.call(this,b)}catch(x){return Promise.reject(x)}for(d=0,g=c.length;d<g;)f=f.then(c[d++],c[d++]);return f}getUri(t){t=Nr(this.defaults,t);const r=qf(t.baseURL,t.url,t.allowAbsoluteUrls);return $f(r,t.params,t.paramsSerializer)}}C.forEach(["delete","get","head","options"],function(t){xo.prototype[t]=function(r,n){return this.request(Nr(n||{},{method:t,url:r,data:(n||{}).data}))}});C.forEach(["post","put","patch"],function(t){function r(n){return function(o,i,a){return this.request(Nr(a||{},{method:t,headers:n?{"Content-Type":"multipart/form-data"}:{},url:o,data:i}))}}xo.prototype[t]=r(),xo.prototype[t+"Form"]=r(!0)});const $s=xo;class _a{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let r;this.promise=new Promise(function(o){r=o});const n=this;this.promise.then(s=>{if(!n._listeners)return;let o=n._listeners.length;for(;o-- >0;)n._listeners[o](s);n._listeners=null}),this.promise.then=s=>{let o;const i=new Promise(a=>{n.subscribe(a),o=a}).then(s);return i.cancel=function(){n.unsubscribe(o)},i},t(function(o,i,a){n.reason||(n.reason=new on(o,i,a),r(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const r=this._listeners.indexOf(t);r!==-1&&this._listeners.splice(r,1)}toAbortSignal(){const t=new AbortController,r=n=>{t.abort(n)};return this.subscribe(r),t.signal.unsubscribe=()=>this.unsubscribe(r),t.signal}static source(){let t;return{token:new _a(function(s){t=s}),cancel:t}}}const lx=_a;function ix(e){return function(r){return e.apply(null,r)}}function ax(e){return C.isObject(e)&&e.isAxiosError===!0}const bi={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(bi).forEach(([e,t])=>{bi[t]=e});const ux=bi;function Xf(e){const t=new $s(e),r=Pf($s.prototype.request,t);return C.extend(r,$s.prototype,t,{allOwnKeys:!0}),C.extend(r,t,null,{allOwnKeys:!0}),r.create=function(s){return Xf(Nr(e,s))},r}const de=Xf(ka);de.Axios=$s;de.CanceledError=on;de.CancelToken=lx;de.isCancel=Wf;de.VERSION=Gf;de.toFormData=Do;de.AxiosError=z;de.Cancel=de.CanceledError;de.all=function(t){return Promise.all(t)};de.spread=ix;de.isAxiosError=ax;de.mergeConfig=Nr;de.AxiosHeaders=ut;de.formToJSON=e=>Hf(C.isHTMLForm(e)?new FormData(e):e);de.getAdapter=Yf.getAdapter;de.HttpStatusCode=ux;de.default=de;const Y=de,cx={BASE:"http://localhost:5000/api",LOGIN:"http://localhost:5000/api/auth/login",FORGET:"http://localhost:5000/api/auth/forgot-password",READING_EXAM:"http://localhost:5002/api/students/exams",PAYMENT:"http://localhost:5003/api/payments",MONITOR:"http://localhost:5004/api/monitor",READING_EVALUATION:"http://localhost:5002/api/evaluations",AUTH:"http://localhost:5000/api/auth",USERS:"http://localhost:5001/api/users",STUDENTS:"http://localhost:5002/api/students",COURSES:"http://localhost:5003/api/courses",PARENTS:"http://localhost:5004/api/parents"},dx={NAME:"local",IS_PROD:!1,IS_DEV:!0,DEBUG:!0,LOG_LEVEL:"debug"},fx={TOKEN_KEY:"token",USER_KEY:"user",SCHOOL_CODE_KEY:"main_code"},px={NAME:"School Management System",VERSION:"1.0.0"},Le={API:cx,ENV:dx,AUTH:fx,APP:px},mx=Le.API.AUTH,I={login:async(e,t,r=null)=>{try{const n={username:e,password:t};r&&(n.main_code=r);const s=await Y.post(`${mx}/login`,n,{withCredentials:!0});if(s.data.token)try{const i=s.data.token.split(".")[1].replace(/-/g,"+").replace(/_/g,"/"),a=decodeURIComponent(atob(i).split("").map(function(f){return"%"+("00"+f.charCodeAt(0).toString(16)).slice(-2)}).join("")),u=JSON.parse(a);if(console.log("Token payload:",u),console.log("User role from token:",u.role),!u.role)throw console.error("Token does not contain role information"),new Error("Invalid token format: missing role");const c=s.data.user;c&&(c.role==="Teacher"||c.role==="Admin")&&(c.schoolcode&&!c.main_code&&(c.main_code=c.schoolcode,delete c.schoolcode),c.main_code&&(localStorage.setItem(Le.AUTH.SCHOOL_CODE_KEY,c.main_code),console.log(`Stored main_code in localStorage: ${c.main_code}`))),localStorage.setItem(Le.AUTH.TOKEN_KEY,s.data.token),localStorage.setItem(Le.AUTH.USER_KEY,JSON.stringify(c||s.data.user))}catch(o){throw console.error("Error decoding token:",o),{error:"Invalid token format. Please contact support."}}return s.data}catch(n){throw n.response?n.response.data:{error:n.error||"Network error"}}},logout:()=>{localStorage.removeItem(Le.AUTH.TOKEN_KEY),localStorage.removeItem(Le.AUTH.USER_KEY),localStorage.removeItem(Le.AUTH.SCHOOL_CODE_KEY)},getCurrentUser:()=>{const e=localStorage.getItem(Le.AUTH.USER_KEY);if(!e)return null;const t=JSON.parse(e);return t&&(t.role==="Teacher"||t.role==="Admin")&&t.schoolcode&&!t.main_code&&(t.main_code=t.schoolcode,delete t.schoolcode,localStorage.setItem(Le.AUTH.USER_KEY,JSON.stringify(t))),t},getToken:()=>localStorage.getItem(Le.AUTH.TOKEN_KEY),isLoggedIn:()=>!!localStorage.getItem(Le.AUTH.TOKEN_KEY),getAuthHeader:()=>{const e=I.getToken();return e?(console.log("Adding Authorization header with token"),{Authorization:`Bearer ${e}`}):(console.warn("No token available for Authorization header"),{})},verifyToken:async()=>{try{const e=I.getToken();if(!e)return{valid:!1,error:"No token found"};try{const r=e.split(".")[1].replace(/-/g,"+").replace(/_/g,"/"),n=decodeURIComponent(atob(r).split("").map(function(o){return"%"+("00"+o.charCodeAt(0).toString(16)).slice(-2)}).join(""));if(!JSON.parse(n).role)return console.warn("Token does not contain role information"),I.logout(),{valid:!1,error:"Invalid token format: missing role"}}catch(t){return console.error("Error decoding token:",t),I.logout(),{valid:!1,error:"Invalid token format"}}return{valid:!0}}catch(e){return console.error("Token verification error:",e),{valid:!1,error:e.message}}},getSchoolCode:()=>{const e=localStorage.getItem(Le.AUTH.SCHOOL_CODE_KEY);if(e)return e;const t=I.getCurrentUser();return t&&t.main_code||null}},Zf=j.createContext(),lr=()=>j.useContext(Zf),hx=({children:e})=>{const[t,r]=j.useState(null),[n,s]=j.useState(!0),[o,i]=j.useState("");j.useEffect(()=>{(async()=>{const g=I.getCurrentUser();if(g){const b=await I.verifyToken();b.valid?r(g):(console.warn("Token verification failed:",b.error),I.logout(),r(null))}s(!1)})()},[]);const a=async(d,g,b=null)=>{try{i("");const x=await I.login(d,g,b);return r(x.user),x}catch(x){throw i(x.error||"Login failed"),x}},u=()=>{I.logout(),r(null)},f={currentUser:t?{...t,get schoolcode(){return this.main_code}}:null,login:a,logout:u,error:o,isLoggedIn:!!t,getSchoolCode:()=>I.getSchoolCode()};return l.jsx(Zf.Provider,{value:f,children:!n&&e})},gx=({children:e,allowedRoles:t=[],redirectPath:r="/login"})=>{const{currentUser:n,isLoading:s}=lr(),o=Cr();return s?l.jsx("div",{className:"flex items-center justify-center min-h-screen",children:l.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})}):n?t.length>0&&!t.includes(n.role)?l.jsx(hi,{to:"/dashboard",replace:!0}):e:l.jsx(hi,{to:r,state:{from:o},replace:!0})},Dr=({id:e,name:t,type:r="text",label:n,value:s,onChange:o,placeholder:i="",error:a="",required:u=!1,disabled:c=!1,className:f="",autoComplete:d="on",maxLength:g,minLength:b,pattern:x,userRole:y=""})=>{const[S,m]=j.useState(!1),p=()=>m(!0),h=()=>m(!1),N=()=>{switch(y){case"super_admin":return{focusRing:"focus:ring-purple-500",focusBorder:"border-purple-500",bgColor:"bg-purple-50"};case"admin":return{focusRing:"focus:ring-green-500",focusBorder:"border-green-500",bgColor:"bg-green-50"};case"teacher":return{focusRing:"focus:ring-indigo-500",focusBorder:"border-indigo-500",bgColor:"bg-indigo-50"};case"student":return{focusRing:"focus:ring-blue-500",focusBorder:"border-blue-500",bgColor:"bg-blue-50"};case"parent":return{focusRing:"focus:ring-teal-500",focusBorder:"border-teal-500",bgColor:"bg-teal-50"};default:return{focusRing:"focus:ring-blue-500",focusBorder:"border-blue-500",bgColor:""}}},{focusRing:w,focusBorder:_,bgColor:T}=N(),R=()=>x||(t==="email"?"[a-z0-9._%+-]+@[a-z0-9.-]+\\.[a-z]{2,}$":t==="phone"?"[0-9]{10,}":null);return l.jsxs("div",{className:`mb-4 ${f}`,children:[n&&l.jsxs("label",{htmlFor:e||t,className:`block text-sm font-medium text-gray-700 mb-1 ${y?`text-${y}-700`:""}`,children:[n,u&&l.jsx("span",{className:"text-red-500 ml-1",children:"*"})]}),l.jsx("input",{id:e||t,name:t,type:r,value:s,onChange:o,onFocus:p,onBlur:h,placeholder:i,disabled:c,required:u,autoComplete:d,maxLength:g,minLength:b,pattern:R(),className:`
          w-full px-3 py-2 border rounded-md shadow-sm
          focus:outline-none focus:ring-2 ${w}
          ${c?"bg-gray-100 cursor-not-allowed":T}
          ${a?"border-red-500":"border-gray-300"}
          ${S?_:""}
        `}),a&&l.jsx("p",{className:"mt-1 text-sm text-red-600",children:a})]})},bt=({children:e,type:t="button",variant:r="primary",size:n="md",disabled:s=!1,loading:o=!1,onClick:i,className:a="",userRole:u="",fullWidth:c=!1,...f})=>{const[d,g]=j.useState(!1),b=()=>g(!0),x=()=>g(!1),y=()=>g(!1),S=()=>{switch(u){case"super_admin":return{primary:"bg-purple-600 hover:bg-purple-700 focus:ring-purple-500 text-white",secondary:"bg-purple-100 hover:bg-purple-200 focus:ring-purple-500 text-purple-800 border border-purple-300",danger:"bg-red-600 hover:bg-red-700 focus:ring-red-500 text-white",success:"bg-green-600 hover:bg-green-700 focus:ring-green-500 text-white",warning:"bg-yellow-500 hover:bg-yellow-600 focus:ring-yellow-500 text-white",info:"bg-blue-600 hover:bg-blue-700 focus:ring-blue-500 text-white"};case"admin":return{primary:"bg-green-600 hover:bg-green-700 focus:ring-green-500 text-white",secondary:"bg-green-100 hover:bg-green-200 focus:ring-green-500 text-green-800 border border-green-300",danger:"bg-red-600 hover:bg-red-700 focus:ring-red-500 text-white",success:"bg-emerald-600 hover:bg-emerald-700 focus:ring-emerald-500 text-white",warning:"bg-yellow-500 hover:bg-yellow-600 focus:ring-yellow-500 text-white",info:"bg-blue-600 hover:bg-blue-700 focus:ring-blue-500 text-white"};case"teacher":return{primary:"bg-indigo-600 hover:bg-indigo-700 focus:ring-indigo-500 text-white",secondary:"bg-indigo-100 hover:bg-indigo-200 focus:ring-indigo-500 text-indigo-800 border border-indigo-300",danger:"bg-red-600 hover:bg-red-700 focus:ring-red-500 text-white",success:"bg-green-600 hover:bg-green-700 focus:ring-green-500 text-white",warning:"bg-yellow-500 hover:bg-yellow-600 focus:ring-yellow-500 text-white",info:"bg-blue-600 hover:bg-blue-700 focus:ring-blue-500 text-white"};case"student":return{primary:"bg-blue-600 hover:bg-blue-700 focus:ring-blue-500 text-white",secondary:"bg-blue-100 hover:bg-blue-200 focus:ring-blue-500 text-blue-800 border border-blue-300",danger:"bg-red-600 hover:bg-red-700 focus:ring-red-500 text-white",success:"bg-green-600 hover:bg-green-700 focus:ring-green-500 text-white",warning:"bg-yellow-500 hover:bg-yellow-600 focus:ring-yellow-500 text-white",info:"bg-cyan-600 hover:bg-cyan-700 focus:ring-cyan-500 text-white"};case"parent":return{primary:"bg-orange-600 hover:bg-orange-700 focus:ring-orange-500 text-white",secondary:"bg-orange-100 hover:bg-orange-200 focus:ring-orange-500 text-orange-800 border border-orange-300",danger:"bg-red-600 hover:bg-red-700 focus:ring-red-500 text-white",success:"bg-green-600 hover:bg-green-700 focus:ring-green-500 text-white",warning:"bg-yellow-500 hover:bg-yellow-600 focus:ring-yellow-500 text-white",info:"bg-blue-600 hover:bg-blue-700 focus:ring-blue-500 text-white"};default:return{primary:"bg-blue-600 hover:bg-blue-700 focus:ring-blue-500 text-white",secondary:"bg-gray-200 hover:bg-gray-300 focus:ring-gray-500 text-gray-800 border border-gray-300",danger:"bg-red-600 hover:bg-red-700 focus:ring-red-500 text-white",success:"bg-green-600 hover:bg-green-700 focus:ring-green-500 text-white",warning:"bg-yellow-500 hover:bg-yellow-600 focus:ring-yellow-500 text-white",info:"bg-blue-600 hover:bg-blue-700 focus:ring-blue-500 text-white"}}},m=()=>{switch(n){case"sm":return"px-3 py-1.5 text-sm";case"lg":return"px-6 py-3 text-lg";default:return"px-4 py-2 text-base"}},p=S(),h=p[r]||p.primary,N=m();return l.jsx("button",{type:t,disabled:s||o,onClick:i,onMouseDown:b,onMouseUp:x,onMouseLeave:y,className:`
        ${c?"w-full":""}
        ${N}
        ${h}
        font-medium rounded-md shadow-sm
        focus:outline-none focus:ring-2 focus:ring-offset-2
        transition-all duration-200
        ${s||o?"opacity-50 cursor-not-allowed":"cursor-pointer"}
        ${d?"transform scale-95":""}
        ${a}
      `,...f,children:o?l.jsxs("div",{className:"flex items-center justify-center",children:[l.jsxs("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[l.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),l.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Loading..."]}):e})},xx=({title:e,subtitle:t,children:r,footer:n,className:s="",headerClassName:o="",bodyClassName:i="",footerClassName:a=""})=>l.jsxs("div",{className:`bg-white rounded-lg shadow-md overflow-hidden ${s}`,children:[(e||t)&&l.jsxs("div",{className:`px-6 py-4 border-b border-gray-200 ${o}`,children:[e&&l.jsx("h3",{className:"text-lg font-medium text-gray-900",children:e}),t&&l.jsx("p",{className:"mt-1 text-sm text-gray-500",children:t})]}),l.jsx("div",{className:`px-6 py-4 ${i}`,children:r}),n&&l.jsx("div",{className:`px-6 py-4 border-t border-gray-200 bg-gray-50 ${a}`,children:n})]}),yx=()=>{const[e,t]=j.useState(""),[r,n]=j.useState(""),[s,o]=j.useState(""),[i,a]=j.useState(!1),{login:u}=lr(),c=ns(),f=async d=>{if(d.preventDefault(),!e||!r){o("Username and password are required");return}try{a(!0),o(""),await u(e,r),c("/dashboard")}catch(g){o(g.error||"Login failed. Please check your credentials.")}finally{a(!1)}};return l.jsx("div",{className:"min-h-screen bg-gray-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:l.jsxs("div",{className:"max-w-md w-full space-y-8",children:[l.jsxs("div",{className:"text-center",children:[l.jsx("h1",{className:"text-3xl font-extrabold text-gray-900",children:Le.APP.NAME}),l.jsx("p",{className:"mt-2 text-sm text-gray-600",children:"Sign in to your account"})]}),l.jsx(xx,{children:l.jsxs("form",{className:"space-y-6",onSubmit:f,children:[s&&l.jsx("div",{className:"bg-red-50 border-l-4 border-red-500 p-4 mb-4",children:l.jsxs("div",{className:"flex",children:[l.jsx("div",{className:"flex-shrink-0",children:l.jsx("svg",{className:"h-5 w-5 text-red-500",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",children:l.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),l.jsx("div",{className:"ml-3",children:l.jsx("p",{className:"text-sm text-red-700",children:s})})]})}),l.jsx(Dr,{id:"username",name:"username",type:"text",label:"Username",value:e,onChange:d=>t(d.target.value),required:!0,autoComplete:"username"}),l.jsx(Dr,{id:"password",name:"password",type:"password",label:"Password",value:r,onChange:d=>n(d.target.value),required:!0,autoComplete:"current-password"}),l.jsx("div",{children:l.jsx(bt,{type:"submit",disabled:i,loading:i,fullWidth:!0,variant:"primary",children:"Sign in"})})]})})]})})},vx=({isOpen:e=!0,onToggle:t,className:r="",variant:n="default"})=>{var h,N;const{currentUser:s,logout:o}=lr(),i=ns(),a=Cr(),[u,c]=j.useState({}),f=()=>{o(),i("/login")},d=w=>{c(_=>({..._,[w]:!_[w]}))},g=w=>a.pathname===w||a.pathname.startsWith(w+"/"),b=()=>{const w=[{key:"dashboard",label:"Dashboard",path:"/dashboard",icon:l.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"})})}];if(!s)return w;switch(s.role){case"Super Admin":return[...w,{key:"users",label:"User Management",icon:l.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"})}),submenu:[{key:"register-admin",label:"Register Admin",path:"/dashboard?tab=users"},{key:"register-teacher",label:"Register Teacher",path:"/dashboard?tab=users"},{key:"all-users",label:"All Users",path:"/dashboard?tab=users"}]},{key:"courses",label:"Course Management",icon:l.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C20.168 18.477 18.582 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"})}),submenu:[{key:"create-course",label:"Create Course",path:"/dashboard?tab=courses"},{key:"course-mapping",label:"Course Mapping",path:"/dashboard?tab=courses"},{key:"all-courses",label:"All Courses",path:"/dashboard?tab=courses"}]},{key:"students",label:"Student Management",path:"/dashboard?tab=students",icon:l.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})})},{key:"parents",label:"Parent Management",path:"/dashboard?tab=parents",icon:l.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})})}];case"Admin":return[...w,{key:"teachers",label:"Teacher Management",icon:l.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})}),submenu:[{key:"register-teacher",label:"Register Teacher",path:"/dashboard?tab=teachers"},{key:"all-teachers",label:"All Teachers",path:"/dashboard?tab=teachers"}]},{key:"students",label:"Student Management",path:"/dashboard?tab=students",icon:l.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})})},{key:"courses",label:"Course Management",path:"/dashboard?tab=courses",icon:l.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C20.168 18.477 18.582 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"})})},{key:"parents",label:"Parent Management",path:"/dashboard?tab=parents",icon:l.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})})}];case"Teacher":return[...w,{key:"students",label:"Student Management",icon:l.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})}),submenu:[{key:"register-student",label:"Register Student",path:"/dashboard?tab=students"},{key:"course-mapping",label:"Course Mapping",path:"/dashboard?tab=courseCreation"},{key:"all-students",label:"All Students",path:"/dashboard?tab=students"}]},{key:"parents",label:"Parent Management",icon:l.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})}),submenu:[{key:"register-parent",label:"Register Parent",path:"/dashboard?tab=parents"},{key:"parent-mapping",label:"Parent Mapping",path:"/dashboard?tab=parentMapping"},{key:"all-parents",label:"All Parents",path:"/dashboard?tab=parents"}]}];case"Student":return[...w,{key:"courses",label:"My Courses",path:"/dashboard?tab=courses",icon:l.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C20.168 18.477 18.582 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"})})},{key:"profile",label:"My Profile",path:"/dashboard?tab=profile",icon:l.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})})}];case"Parent":return[...w,{key:"children",label:"My Children",path:"/dashboard?tab=children",icon:l.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})})},{key:"profile",label:"My Profile",path:"/dashboard?tab=profile",icon:l.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})})}];default:return w}},x=()=>{if(!s)return"bg-gray-800 text-white";switch(s.role){case"Super Admin":return"bg-purple-800 text-white";case"Admin":return"bg-green-800 text-white";case"Teacher":return"bg-indigo-800 text-white";case"Student":return"bg-blue-800 text-white";case"Parent":return"bg-orange-800 text-white";default:return"bg-gray-800 text-white"}},y=b(),S=x(),p=(()=>{switch(n){case"compact":return"w-16";case"floating":return"w-64 rounded-lg shadow-xl m-4";default:return"w-64"}})();return l.jsxs("aside",{className:`${S} ${p} flex-shrink-0 transition-all duration-300 ease-in-out ${e?"translate-x-0":"-translate-x-full"} md:translate-x-0 fixed md:static h-full z-20 ${r}`,children:[l.jsx("div",{className:"p-4 border-b border-opacity-20 border-white",children:l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{className:"flex items-center space-x-3",children:[l.jsx("div",{className:"w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center",children:l.jsx("span",{className:"text-sm font-bold",children:Le.APP.NAME.charAt(0)})}),n!=="compact"&&l.jsxs("div",{children:[l.jsx("h2",{className:"text-lg font-semibold",children:Le.APP.NAME}),s&&l.jsx("p",{className:"text-xs opacity-75",children:s.role})]})]}),t&&l.jsx("button",{onClick:t,className:"md:hidden text-white hover:bg-white hover:bg-opacity-20 p-1 rounded focus:outline-none",children:l.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M6 18L18 6M6 6l12 12"})})})]})}),l.jsx("nav",{className:"flex-1 p-4 space-y-2 overflow-y-auto",children:y.map(w=>l.jsx("div",{children:w.submenu?l.jsxs("div",{children:[l.jsxs("button",{onClick:()=>d(w.key),className:"w-full flex items-center justify-between px-3 py-2 rounded-lg hover:bg-white hover:bg-opacity-10 transition-colors duration-200",children:[l.jsxs("div",{className:"flex items-center space-x-3",children:[w.icon,n!=="compact"&&l.jsx("span",{className:"text-sm font-medium",children:w.label})]}),n!=="compact"&&l.jsx("svg",{className:`w-4 h-4 transition-transform duration-200 ${u[w.key]?"rotate-180":""}`,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M19 9l-7 7-7-7"})})]}),u[w.key]&&n!=="compact"&&l.jsx("div",{className:"ml-6 mt-2 space-y-1",children:w.submenu.map(_=>l.jsx(Wu,{to:_.path,className:`block px-3 py-2 text-sm rounded-lg hover:bg-white hover:bg-opacity-10 transition-colors duration-200 ${g(_.path)?"bg-white bg-opacity-20":""}`,children:_.label},_.key))})]}):l.jsxs(Wu,{to:w.path,className:`flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-white hover:bg-opacity-10 transition-colors duration-200 ${g(w.path)?"bg-white bg-opacity-20":""}`,children:[w.icon,n!=="compact"&&l.jsx("span",{className:"text-sm font-medium",children:w.label})]})},w.key))}),l.jsxs("div",{className:"p-4 border-t border-opacity-20 border-white",children:[s&&n!=="compact"&&l.jsx("div",{className:"mb-3 p-3 bg-white bg-opacity-10 rounded-lg",children:l.jsxs("div",{className:"flex items-center space-x-3",children:[l.jsx("div",{className:"w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center",children:l.jsx("span",{className:"text-sm font-semibold",children:((h=s.username)==null?void 0:h.charAt(0).toUpperCase())||"U"})}),l.jsxs("div",{className:"flex-1 min-w-0",children:[l.jsx("p",{className:"text-sm font-medium truncate",children:s.username}),l.jsx("p",{className:"text-xs opacity-75 truncate",children:s.email})]})]})}),l.jsx(bt,{onClick:f,variant:"secondary",size:"sm",fullWidth:n!=="compact",userRole:(N=s==null?void 0:s.role)==null?void 0:N.toLowerCase().replace(" ","_"),className:"bg-white bg-opacity-20 hover:bg-opacity-30 text-white border-white border-opacity-30",children:n==="compact"?l.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"})}):"Logout"})]})]})},os=({children:e,title:t})=>{const{currentUser:r}=lr(),[n,s]=j.useState(!1),o=()=>{s(!n)};return l.jsxs("div",{className:"flex h-screen bg-gray-100",children:[l.jsx(vx,{isOpen:n,onToggle:o,variant:"default"}),l.jsxs("div",{className:"flex-1 flex flex-col overflow-hidden",children:[l.jsx("header",{className:"bg-white shadow-md",children:l.jsxs("div",{className:"flex items-center justify-between p-4",children:[l.jsxs("div",{className:"flex items-center",children:[l.jsx("button",{onClick:o,className:"text-gray-500 focus:outline-none md:hidden",children:l.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M4 6h16M4 12h16M4 18h16"})})}),l.jsx("h1",{className:"text-xl font-semibold text-gray-800 ml-4",children:t})]}),l.jsx("div",{className:"flex items-center",children:l.jsxs("span",{className:"text-gray-600 mr-4",children:["Welcome, ",r?r.username:"User"]})})]})}),l.jsx("main",{className:"flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6",children:e})]})]})},wx=({id:e,name:t,label:r,value:n,onChange:s,placeholder:o="",error:i="",required:a=!1,disabled:u=!1,className:c="",min:f,max:d,userRole:g="",showIcon:b=!0})=>{const[x,y]=j.useState(!1),S=()=>y(!0),m=()=>y(!1),p=()=>{switch(g){case"super_admin":return{focusRing:"focus:ring-purple-500",focusBorder:"focus:border-purple-500",bgColor:"bg-purple-50",labelColor:"text-purple-700",iconColor:"text-purple-500"};case"admin":return{focusRing:"focus:ring-green-500",focusBorder:"focus:border-green-500",bgColor:"bg-green-50",labelColor:"text-green-700",iconColor:"text-green-500"};case"teacher":return{focusRing:"focus:ring-indigo-500",focusBorder:"focus:border-indigo-500",bgColor:"bg-indigo-50",labelColor:"text-indigo-700",iconColor:"text-indigo-500"};case"student":return{focusRing:"focus:ring-blue-500",focusBorder:"focus:border-blue-500",bgColor:"bg-blue-50",labelColor:"text-blue-700",iconColor:"text-blue-500"};case"parent":return{focusRing:"focus:ring-orange-500",focusBorder:"focus:border-orange-500",bgColor:"bg-orange-50",labelColor:"text-orange-700",iconColor:"text-orange-500"};default:return{focusRing:"focus:ring-blue-500",focusBorder:"focus:border-blue-500",bgColor:"bg-gray-50",labelColor:"text-gray-700",iconColor:"text-gray-500"}}},{focusRing:h,focusBorder:N,bgColor:w,labelColor:_,iconColor:T}=p();return l.jsxs("div",{className:`mb-4 ${c}`,children:[r&&l.jsxs("label",{htmlFor:e||t,className:`block text-sm font-medium mb-2 ${_}`,children:[r,a&&l.jsx("span",{className:"text-red-500 ml-1",children:"*"})]}),l.jsxs("div",{className:"relative",children:[l.jsx("input",{id:e||t,name:t,type:"date",value:n,onChange:s,onFocus:S,onBlur:m,placeholder:o,disabled:u,required:a,min:f,max:d,className:`
            w-full px-3 py-2 border rounded-md shadow-sm
            focus:outline-none focus:ring-2 ${h}
            ${u?"bg-gray-100 cursor-not-allowed":w}
            ${i?"border-red-500":"border-gray-300"}
            ${x?N:""}
            ${b?"pr-10":""}
          `}),b&&l.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:l.jsx("svg",{className:`h-5 w-5 ${T}`,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})})})]}),i&&l.jsx("p",{className:"mt-1 text-sm text-red-600",children:i})]})},Nl="http://localhost:5001/api/users",vt={registerUser:async e=>{try{return(await Y.post(`${Nl}/register`,e,{headers:I.getAuthHeader(),withCredentials:!0})).data}catch(t){throw t.response?t.response.data:{error:"Network error"}}},getUsers:async()=>{try{const e=await Y.get(`${Nl}/users`,{headers:I.getAuthHeader(),withCredentials:!0});return console.log("Successfully retrieved users:",e.data),e.data}catch(e){if(e.response&&e.response.status===403){console.warn("Permission denied: User doesn't have access to view all users"),console.error("Error details:",e.response.data);const t=JSON.parse(localStorage.getItem("user")||"{}");return console.log("Current user role from localStorage:",t.role),{users:[]}}throw e.response?e.response.data:{error:"Network error"}}},getUser:async e=>{try{return(await Y.get(`${Nl}/users/${e}`,{headers:I.getAuthHeader(),withCredentials:!0})).data}catch(t){throw t.response?t.response.data:{error:"Network error"}}}},yn="http://localhost:5003/api/courses",ct={createCourse:async e=>{try{return(await Y.post(`${yn}/courses`,e,{headers:I.getAuthHeader(),withCredentials:!0})).data}catch(t){throw t.response?t.response.data:{error:"Network error"}}},getCourses:async()=>{try{return(await Y.get(`${yn}/courses`,{headers:I.getAuthHeader(),withCredentials:!0})).data}catch(e){if(!e.response)return console.warn("CORS or network error when fetching courses"),{courses:[]};throw e.response?e.response.data:{error:"Network error"}}},getCourse:async e=>{try{return(await Y.get(`${yn}/courses/${e}`,{headers:I.getAuthHeader(),withCredentials:!0})).data}catch(t){throw t.response?t.response.data:{error:"Network error"}}},mapStudentToCourse:async(e,t)=>{try{return(await Y.post(`${yn}/map-student`,{student_id:e,course_id:t},{headers:I.getAuthHeader(),withCredentials:!0})).data}catch(r){throw r.response?r.response.data:{error:"Network error"}}},getStudentCourses:async e=>{try{return(await Y.get(`${yn}/student-courses/${e}`,{headers:I.getAuthHeader(),withCredentials:!0})).data}catch(t){throw t.response?t.response.data:{error:"Network error"}}}},ir="http://localhost:5002/api/students",Pe={registerStudent:async e=>{try{return(await Y.post(`${ir}/students`,e,{headers:I.getAuthHeader()})).data}catch(t){throw t.response?t.response.data:{error:"Network error"}}},getStudents:async()=>{try{return(await Y.get(`${ir}/students`,{headers:I.getAuthHeader()})).data}catch(e){throw e.response?e.response.data:{error:"Network error"}}},getStudent:async e=>{try{return(await Y.get(`${ir}/students/${e}`,{headers:I.getAuthHeader()})).data}catch(t){throw t.response?t.response.data:{error:"Network error"}}},mapParentToStudent:async(e,t,r=null)=>{try{return(await Y.post(`${ir}/map-parent`,{parent_id:e,student_id:t,relationship:r},{headers:I.getAuthHeader()})).data}catch(n){throw n.response?n.response.data:{error:"Network error"}}},getParentStudents:async e=>{try{return(await Y.get(`${ir}/parent-students/${e}`,{headers:I.getAuthHeader()})).data}catch(t){throw t.response?t.response.data:{error:"Network error"}}},getStudentProfile:async()=>{try{return(await Y.get(`${ir}/student-profile`,{headers:I.getAuthHeader(),withCredentials:!0})).data}catch{console.warn("Error getting student profile, falling back to getStudents");try{const t=I.getCurrentUser();if(!t)throw{error:"User not authenticated"};const s=((await Pe.getStudents()).students||[]).find(o=>o.user_id===parseInt(t.id));if(!s)throw{error:"Student not found"};return{student:s}}catch(t){throw t.error?t:{error:"Failed to get student profile"}}}},getStudentParents:async e=>{try{return(await Y.get(`${ir}/student-parents/${e}`,{headers:I.getAuthHeader()})).data}catch(t){throw t.response?t.response.data:{error:"Network error"}}}},Ss="http://localhost:5004/api/parents",jr={registerParent:async e=>{try{return(await Y.post(`${Ss}/parents`,e,{headers:I.getAuthHeader()})).data}catch(t){throw t.response?t.response.data:{error:"Network error"}}},getParents:async()=>{try{return(await Y.get(`${Ss}/parents`,{headers:I.getAuthHeader()})).data}catch(e){throw e.response?e.response.data:{error:"Network error"}}},getParent:async e=>{try{return(await Y.get(`${Ss}/parents/${e}`,{headers:I.getAuthHeader()})).data}catch(t){throw t.response?t.response.data:{error:"Network error"}}},getParentByUserId:async e=>{try{return(await Y.get(`${Ss}/parents/user/${e}`,{headers:I.getAuthHeader()})).data}catch(t){throw t.response?t.response.data:{error:"Network error"}}}},Nx=()=>{const{currentUser:e}=lr(),[t,r]=j.useState("users"),[n,s]=j.useState([]),[o,i]=j.useState(!1),[a,u]=j.useState(""),[c,f]=j.useState({username:"",password:"",email:"",role:"Admin",is_admin:!1,course:""}),[d,g]=j.useState([]),[b,x]=j.useState(!1),[y,S]=j.useState(""),[m,p]=j.useState({name:"",description:""}),[h,N]=j.useState([]),[w,_]=j.useState(!1),[T,R]=j.useState(""),[D,F]=j.useState({user_id:"",first_name:"",last_name:"",date_of_birth:"",address:"",phone:""}),[ae,Se]=j.useState([]),[ve,X]=j.useState(!1),[we,se]=j.useState(""),[oe,L]=j.useState({user_id:"",first_name:"",last_name:"",occupation:"",address:"",phone:""}),[M,A]=j.useState({parent_id:"",student_id:"",relationship:"Parent"}),[V,H]=j.useState([]),[he,ge]=j.useState({student_id:"",course_id:""}),rt=async()=>{try{i(!0),u("");const v=await vt.getUsers();s(v.users||[])}catch(v){u(v.error||"Failed to load users")}finally{i(!1)}},Te=async()=>{try{x(!0),S("");const v=await ct.getCourses();g(v.courses||[])}catch(v){S(v.error||"Failed to load courses")}finally{x(!1)}},k=v=>{const{name:K,value:He,type:Ft,checked:Ho}=v.target;f({...c,[K]:Ft==="checkbox"?Ho:He})},B=v=>{const{name:K,value:He}=v.target;p({...m,[K]:He})},fe=async v=>{v.preventDefault();try{i(!0),u(""),await vt.registerUser({...c,main_code:e==null?void 0:e.main_code}),f({username:"",password:"",email:"",role:"Admin",is_admin:!1,course:""}),u("User registered successfully"),await rt()}catch(K){u(K.error||"Failed to register user")}finally{i(!1)}},pt=async v=>{v.preventDefault();try{x(!0),S(""),await ct.createCourse({...m,main_code:e==null?void 0:e.main_code}),p({name:"",description:""}),await Te()}catch(K){S(K.error||"Failed to create course")}finally{x(!1)}},Ot=async()=>{try{_(!0),R("");const v=await Pe.getStudents();N(v.students||[])}catch(v){R(v.error||"Failed to load students")}finally{_(!1)}},Mt=async()=>{try{X(!0),se("");const v=await jr.getParents();Se(v.parents||[])}catch(v){se(v.error||"Failed to load parents")}finally{X(!1)}},ls=v=>{const{name:K,value:He}=v.target;F({...D,[K]:He})},E=v=>{const{name:K,value:He}=v.target;L({...oe,[K]:He})},U=async v=>{const{name:K,value:He}=v.target;if(A({...M,[K]:He}),K==="student_id"&&He)try{X(!0);const Ft=await Pe.getStudentParents(He);if(H(Ft.parents||[]),Ft.parents&&Ft.parents.length>0){const Ho=Ft.parents.map(Wo=>`${Wo.first_name} ${Wo.last_name} (${Wo.relationship})`).join(", ");se(`Note: This student already has the following parents mapped: ${Ho}`)}else se("")}catch(Ft){console.error("Error fetching student parents:",Ft)}finally{X(!1)}},ue=v=>{const{name:K,value:He}=v.target;ge({...he,[K]:He})},mt=async v=>{v.preventDefault();try{_(!0),R("");const K=await vt.registerUser({...c,role:"Student",main_code:e==null?void 0:e.main_code});await Pe.registerStudent({...D,user_id:K.user.id,main_code:e==null?void 0:e.main_code}),f({username:"",password:"",email:"",role:"Student"}),F({user_id:"",first_name:"",last_name:"",date_of_birth:"",address:"",phone:""}),R("Student registered successfully"),await Ot()}catch(K){R(K.error||"Failed to register student")}finally{_(!1)}},ln=async v=>{v.preventDefault();try{X(!0),se("");const K=await vt.registerUser({...c,role:"Parent",main_code:e==null?void 0:e.main_code});await jr.registerParent({...oe,user_id:K.user.id,main_code:e==null?void 0:e.main_code}),f({username:"",password:"",email:"",role:"Parent"}),L({user_id:"",first_name:"",last_name:"",occupation:"",address:"",phone:""}),se("Parent registered successfully"),await Mt()}catch(K){se(K.error||"Failed to register parent")}finally{X(!1)}},an=async v=>{v.preventDefault();try{X(!0),se(""),await Pe.mapParentToStudent(M.parent_id,M.student_id,M.relationship||"Parent"),A({parent_id:"",student_id:"",relationship:"Parent"}),H([]),se("Parent mapped to student successfully")}catch(K){se(K.error||"Failed to map parent to student")}finally{X(!1)}},ep=async v=>{v.preventDefault();try{x(!0),S(""),await ct.mapStudentToCourse(he.student_id,he.course_id),ge({student_id:"",course_id:""}),S("Student mapped to course successfully")}catch(K){S(K.error||"Failed to map student to course")}finally{x(!1)}};return j.useEffect(()=>{t==="users"?rt():t==="courses"?(Te(),Ot()):t==="students"?Ot():t==="parents"&&(Mt(),Ot())},[t]),l.jsx(os,{title:"Super Admin Dashboard",children:l.jsxs("div",{className:"container mx-auto px-4 py-8",children:[l.jsx("div",{className:"mb-8 rounded-2xl bg-white shadow-sm",children:l.jsx("div",{className:"flex flex-wrap border-b-2 border-gray-100",children:["users","courses","students","parents"].map(v=>l.jsxs(bt,{variant:t===v?"primary":"secondary",size:"md",userRole:"super_admin",className:`relative px-6 py-4 text-sm font-semibold transition-all duration-300 ${t===v?"after:absolute after:bottom-0 after:left-0 after:h-0.5 after:w-full after:bg-purple-600":""}`,onClick:()=>r(v),children:[v.charAt(0).toUpperCase()+v.slice(1)," Management"]},v))})}),t==="users"&&l.jsxs("div",{className:"space-y-8",children:[l.jsxs("div",{className:"rounded-2xl bg-white p-8 shadow-lg transition-all duration-300 hover:shadow-xl",children:[l.jsx("h2",{className:"mb-6 text-2xl font-bold text-gray-800",children:"Register New User"}),a&&l.jsx("div",{className:`mb-6 rounded-lg p-4 ${a.includes("successfully")?"bg-green-50 text-green-700":"bg-red-50 text-red-700"}`,role:"alert",children:a}),l.jsxs("form",{onSubmit:fe,className:"space-y-6",children:[l.jsxs("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2",children:[[{id:"username",label:"Username",type:"text"},{id:"password",label:"Password",type:"password"},{id:"email",label:"Email",type:"email"}].map(v=>l.jsx(Dr,{id:v.id,name:v.id,type:v.type,label:v.label,value:c[v.id],onChange:k,required:!0,userRole:"super_admin"},v.id)),l.jsxs("div",{className:"space-y-2",children:[l.jsx("label",{htmlFor:"role",className:"block text-sm font-medium text-gray-700",children:"Role"}),l.jsx("select",{className:"w-full rounded-lg border-transparent bg-gray-100 py-3 px-4 text-sm focus:border-blue-500 focus:ring-0",id:"role",name:"role",value:c.role,onChange:k,required:!0,children:["Admin","Teacher","Student","Parent"].map(v=>l.jsx("option",{value:v,children:v},v))})]}),c.role==="Teacher"&&l.jsxs("div",{className:"space-y-2",children:[l.jsx("label",{htmlFor:"course",className:"block text-sm font-medium text-gray-700",children:"Course"}),l.jsxs("select",{className:"w-full rounded-lg border-transparent bg-gray-100 py-3 px-4 text-sm focus:border-blue-500 focus:ring-0",id:"course",name:"course",value:c.course,onChange:k,required:!0,children:[l.jsx("option",{value:"",children:"Select Course"}),["Neet","Jee"].map(v=>l.jsx("option",{value:v,children:v},v))]})]}),l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsx("input",{type:"checkbox",className:"h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500",id:"is_admin",name:"is_admin",checked:c.is_admin,onChange:k}),l.jsx("label",{htmlFor:"is_admin",className:"text-sm text-gray-700",children:"Is Admin (for Teachers)"})]})]}),l.jsx(bt,{type:"submit",variant:"primary",size:"md",userRole:"super_admin",disabled:o,loading:o,children:"Register User"})]})]}),l.jsxs("div",{className:"rounded-2xl bg-white p-8 shadow-lg transition-all duration-300 hover:shadow-xl",children:[l.jsx("h2",{className:"mb-6 text-2xl font-bold text-gray-800",children:"All Users"}),o?l.jsx("div",{className:"flex items-center justify-center py-8",children:l.jsxs("svg",{className:"h-8 w-8 animate-spin text-blue-600",viewBox:"0 0 24 24",fill:"none",children:[l.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),l.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8v8h8a8 8 0 01-8 8 8 8 0 01-8-8z"})]})}):l.jsx("div",{className:"overflow-x-auto",children:l.jsxs("table",{className:"w-full border-collapse",children:[l.jsx("thead",{children:l.jsx("tr",{className:"border-b bg-gray-50",children:["ID","Username","Email","Role","Course","Is Admin"].map(v=>l.jsx("th",{className:"px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider",children:v},v))})}),l.jsx("tbody",{children:n.map(v=>l.jsxs("tr",{className:"border-b transition-colors hover:bg-gray-50",children:[l.jsx("td",{className:"px-6 py-4 text-sm text-gray-900",children:v.id}),l.jsx("td",{className:"px-6 py-4 text-sm font-medium text-gray-900",children:v.username}),l.jsx("td",{className:"px-6 py-4 text-sm text-gray-600",children:v.email}),l.jsx("td",{className:"px-6 py-4",children:l.jsx("span",{className:`inline-flex rounded-full px-3 py-1 text-xs font-medium ${v.role==="Super Admin"?"bg-red-100 text-red-800":v.role==="Admin"?"bg-blue-100 text-blue-800":v.role==="Teacher"?"bg-green-100 text-green-800":v.role==="Student"?"bg-purple-100 text-purple-800":"bg-teal-100 text-teal-800"}`,children:v.role})}),l.jsx("td",{className:"px-6 py-4 text-sm",children:v.role==="Teacher"&&v.course?l.jsx("span",{className:"rounded-full bg-yellow-100 px-3 py-1 text-xs font-medium text-yellow-800",children:v.course}):l.jsx("span",{className:"text-gray-400",children:"-"})}),l.jsx("td",{className:"px-6 py-4",children:l.jsx("span",{className:`inline-flex rounded-full px-3 py-1 text-xs font-medium ${v.is_admin?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"}`,children:v.is_admin?"Yes":"No"})})]},v.id))})]})})]})]}),t==="courses"&&l.jsxs("div",{className:"space-y-8",children:[l.jsxs("div",{className:"rounded-2xl bg-white p-8 shadow-lg transition-all duration-300 hover:shadow-xl",children:[l.jsx("h2",{className:"mb-6 text-2xl font-bold text-gray-800",children:"Create New Course"}),y&&l.jsx("div",{className:`mb-6 rounded-lg p-4 ${y.includes("successfully")?"bg-green-50 text-green-700":"bg-red-50 text-red-700"}`,role:"alert",children:y}),l.jsxs("form",{onSubmit:pt,className:"space-y-6",children:[l.jsxs("div",{className:"grid grid-cols-1 gap-6",children:[l.jsx(Dr,{id:"name",name:"name",type:"text",label:"Course Name",value:m.name,onChange:B,required:!0,userRole:"super_admin"}),l.jsxs("div",{className:"space-y-2",children:[l.jsx("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700",children:"Description"}),l.jsx("textarea",{className:"w-full rounded-lg border-transparent bg-gray-100 py-3 px-4 text-sm focus:border-blue-500 focus:ring-0",id:"description",name:"description",value:m.description,onChange:B,rows:"4"})]})]}),l.jsx(bt,{type:"submit",variant:"primary",size:"md",userRole:"super_admin",disabled:b,loading:b,children:"Create Course"})]})]}),l.jsxs("div",{className:"rounded-2xl bg-white p-8 shadow-lg transition-all duration-300 hover:shadow-xl",children:[l.jsx("h2",{className:"mb-6 text-2xl font-bold text-gray-800",children:"Map Student to Course"}),y&&l.jsx("div",{className:`mb-6 rounded-lg p-4 ${y.includes("successfully")?"bg-green-50 text-green-700":"bg-red-50 text-red-700"}`,role:"alert",children:y}),l.jsxs("form",{onSubmit:ep,className:"space-y-6",children:[l.jsxs("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2",children:[l.jsxs("div",{className:"space-y-2",children:[l.jsx("label",{htmlFor:"student_id",className:"block text-sm font-medium text-gray-700",children:"Student"}),l.jsxs("select",{className:"w-full rounded-lg border-transparent bg-gray-100 py-3 px-4 text-sm focus:border-blue-500 focus:ring-0",id:"student_id",name:"student_id",value:he.student_id,onChange:ue,required:!0,children:[l.jsx("option",{value:"",children:"Select Student"}),h.map(v=>l.jsxs("option",{value:v.id,children:[v.first_name," ",v.last_name]},v.id))]})]}),l.jsxs("div",{className:"space-y-2",children:[l.jsx("label",{htmlFor:"course_id",className:"block text-sm font-medium text-gray-700",children:"Course"}),l.jsxs("select",{className:"w-full rounded-lg border-transparent bg-gray-100 py-3 px-4 text-sm focus:border-blue-500 focus:ring-0",id:"course_id",name:"course_id",value:he.course_id,onChange:ue,required:!0,children:[l.jsx("option",{value:"",children:"Select Course"}),d.map(v=>l.jsx("option",{value:v.id,children:v.name},v.id))]})]})]}),l.jsx(bt,{type:"submit",variant:"primary",size:"md",userRole:"super_admin",disabled:b,loading:b,children:"Map Student to Course"})]})]}),l.jsxs("div",{className:"rounded-2xl bg-white p-8 shadow-lg transition-all duration-300 hover:shadow-xl",children:[l.jsx("h2",{className:"mb-6 text-2xl font-bold text-gray-800",children:"All Courses"}),b?l.jsx("div",{className:"flex items-center justify-center py-8",children:l.jsxs("svg",{className:"h-8 w-8 animate-spin text-blue-600",viewBox:"0 0 24 24",fill:"none",children:[l.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),l.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8v8h8a8 8 0 01-8 8 8 8 0 01-8-8z"})]})}):l.jsx("div",{className:"overflow-x-auto",children:l.jsxs("table",{className:"w-full border-collapse",children:[l.jsx("thead",{children:l.jsx("tr",{className:"border-b bg-gray-50",children:["ID","Name","Description"].map(v=>l.jsx("th",{className:"px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider",children:v},v))})}),l.jsx("tbody",{children:d.map(v=>l.jsxs("tr",{className:"border-b transition-colors hover:bg-gray-50",children:[l.jsx("td",{className:"px-6 py-4 text-sm text-gray-900",children:v.id}),l.jsx("td",{className:"px-6 py-4 text-sm font-medium text-gray-900",children:v.name}),l.jsx("td",{className:"px-6 py-4 text-sm text-gray-600",children:v.description})]},v.id))})]})})]})]}),t==="students"&&l.jsxs("div",{className:"space-y-8",children:[l.jsxs("div",{className:"rounded-2xl bg-white p-8 shadow-lg transition-all duration-300 hover:shadow-xl",children:[l.jsx("h2",{className:"mb-6 text-2xl font-bold text-gray-800",children:"Register New Student"}),T&&l.jsx("div",{className:`mb-6 rounded-lg p-4 ${T.includes("successfully")?"bg-green-50 text-green-700":"bg-red-50 text-red-700"}`,role:"alert",children:T}),l.jsxs("form",{onSubmit:mt,className:"space-y-6",children:[l.jsxs("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2",children:[[{id:"username",label:"Username",type:"text",state:c},{id:"password",label:"Password",type:"password",state:c},{id:"email",label:"Email",type:"email",state:c},{id:"first_name",label:"First Name",type:"text",state:D},{id:"last_name",label:"Last Name",type:"text",state:D},{id:"address",label:"Address",type:"text",state:D},{id:"phone",label:"Phone",type:"text",state:D}].map(v=>l.jsx(Dr,{id:v.id,name:v.id,type:v.type,label:v.label,value:v.state[v.id],onChange:v.state===c?k:ls,required:v.id!=="address"&&v.id!=="phone",userRole:"super_admin"},v.id)),l.jsx(wx,{id:"date_of_birth",name:"date_of_birth",label:"Date of Birth",value:D.date_of_birth,onChange:ls,userRole:"super_admin"})]}),l.jsx(bt,{type:"submit",variant:"primary",size:"md",userRole:"super_admin",disabled:w,loading:w,children:"Register Student"})]})]}),l.jsxs("div",{className:"rounded-2xl bg-white p-8 shadow-lg transition-all duration-300 hover:shadow-xl",children:[l.jsx("h2",{className:"mb-6 text-2xl font-bold text-gray-800",children:"All Students"}),w?l.jsx("div",{className:"flex items-center justify-center py-8",children:l.jsxs("svg",{className:"h-8 w-8 animate-spin text-blue-600",viewBox:"0 0 24 24",fill:"none",children:[l.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),l.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8v8h8a8 8 0 01-8 8 8 8 0 01-8-8z"})]})}):l.jsx("div",{className:"overflow-x-auto",children:l.jsxs("table",{className:"w-full border-collapse",children:[l.jsx("thead",{children:l.jsx("tr",{className:"border-b bg-gray-50",children:["ID","Name","Date of Birth","Address","Phone"].map(v=>l.jsx("th",{className:"px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider",children:v},v))})}),l.jsx("tbody",{children:h.map(v=>l.jsxs("tr",{className:"border-b transition-colors hover:bg-gray-50",children:[l.jsx("td",{className:"px-6 py-4 text-sm text-gray-900",children:v.id}),l.jsxs("td",{className:"px-6 py-4 text-sm font-medium text-gray-900",children:[v.first_name," ",v.last_name]}),l.jsx("td",{className:"px-6 py-4 text-sm text-gray-600",children:v.date_of_birth}),l.jsx("td",{className:"px-6 py-4 text-sm text-gray-600",children:v.address}),l.jsx("td",{className:"px-6 py-4 text-sm text-gray-600",children:v.phone})]},v.id))})]})})]})]}),t==="parents"&&l.jsxs("div",{className:"space-y-8",children:[l.jsxs("div",{className:"rounded-2xl bg-white p-8 shadow-lg transition-all duration-300 hover:shadow-xl",children:[l.jsx("h2",{className:"mb-6 text-2xl font-bold text-gray-800",children:"Register New Parent"}),we&&l.jsx("div",{className:`mb-6 rounded-lg p-4 ${we.includes("successfully")?"bg-green-50 text-green-700":we.includes("Note:")?"bg-blue-50 text-blue-700":"bg-red-50 text-red-700"}`,role:"alert",children:we}),l.jsxs("form",{onSubmit:ln,className:"space-y-6",children:[l.jsx("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2",children:[{id:"username",label:"Username",type:"text",state:c},{id:"password",label:"Password",type:"password",state:c},{id:"email",label:"Email",type:"email",state:c},{id:"first_name",label:"First Name",type:"text",state:oe},{id:"last_name",label:"Last Name",type:"text",state:oe},{id:"occupation",label:"Occupation",type:"text",state:oe},{id:"address",label:"Address",type:"text",state:oe},{id:"phone",label:"Phone",type:"text",state:oe}].map(v=>l.jsx(Dr,{id:v.id,name:v.id,type:v.type,label:v.label,value:v.state[v.id],onChange:v.state===c?k:E,required:v.id!=="occupation"&&v.id!=="address"&&v.id!=="phone",userRole:"super_admin"},v.id))}),l.jsx(bt,{type:"submit",variant:"primary",size:"md",userRole:"super_admin",disabled:ve,loading:ve,children:"Register Parent"})]})]}),l.jsxs("div",{className:"rounded-2xl bg-white p-8 shadow-lg transition-all duration-300 hover:shadow-xl",children:[l.jsx("h2",{className:"mb-6 text-2xl font-bold text-gray-800",children:"Map Parent to Student"}),we&&l.jsx("div",{className:`mb-6 rounded-lg p-4 ${we.includes("successfully")?"bg-green-50 text-green-700":we.includes("Note:")?"bg-blue-50 text-blue-700":"bg-red-50 text-red-700"}`,role:"alert",children:we}),l.jsxs("form",{onSubmit:an,className:"space-y-6",children:[l.jsxs("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2",children:[l.jsxs("div",{className:"space-y-2",children:[l.jsx("label",{htmlFor:"student_id",className:"block text-sm font-medium text-gray-700",children:"Student"}),l.jsxs("select",{className:"w-full rounded-lg border-transparent bg-gray-100 py-3 px-4 text-sm focus:border-blue-500 focus:ring-0",id:"student_id",name:"student_id",value:M.student_id,onChange:U,required:!0,children:[l.jsx("option",{value:"",children:"Select Student"}),h.map(v=>l.jsxs("option",{value:v.id,children:[v.first_name," ",v.last_name]},v.id))]})]}),l.jsxs("div",{className:"space-y-2",children:[l.jsx("label",{htmlFor:"parent_id",className:"block text-sm font-medium text-gray-700",children:"Parent"}),l.jsxs("select",{className:"w-full rounded-lg border-transparent bg-gray-100 py-3 px-4 text-sm focus:border-blue-500 focus:ring-0",id:"parent_id",name:"parent_id",value:M.parent_id,onChange:U,required:!0,children:[l.jsx("option",{value:"",children:"Select Parent"}),ae.map(v=>l.jsxs("option",{value:v.id,children:[v.first_name," ",v.last_name]},v.id))]})]}),l.jsxs("div",{className:"space-y-2",children:[l.jsx("label",{htmlFor:"relationship",className:"block text-sm font-medium text-gray-700",children:"Relationship"}),l.jsx("select",{className:"w-full rounded-lg border-transparent bg-gray-100 py-3 px-4 text-sm focus:border-blue-500 focus:ring-0",id:"relationship",name:"relationship",value:M.relationship,onChange:U,required:!0,children:["Parent","Father","Mother","Guardian"].map(v=>l.jsx("option",{value:v,children:v},v))})]})]}),V.length>0&&l.jsxs("div",{className:"mt-6 rounded-lg bg-blue-50 p-6",children:[l.jsx("h3",{className:"mb-3 text-lg font-semibold text-blue-800",children:"Existing Parents for Selected Student"}),l.jsx("ul",{className:"list-disc space-y-2 pl-5",children:V.map(v=>l.jsxs("li",{className:"text-sm text-blue-600",children:[v.first_name," ",v.last_name," -"," ",l.jsx("span",{className:"font-medium",children:v.relationship})]},v.id))})]}),l.jsx(bt,{type:"submit",variant:"primary",size:"md",userRole:"super_admin",disabled:ve,loading:ve,children:"Map Parent to Student"})]})]}),l.jsxs("div",{className:"rounded-2xl bg-white p-8 shadow-lg transition-all duration-300 hover:shadow-xl",children:[l.jsx("h2",{className:"mb-6 text-2xl font-bold text-gray-800",children:"All Parents"}),ve?l.jsx("div",{className:"flex items-center justify-center py-8",children:l.jsxs("svg",{className:"h-8 w-8 animate-spin text-blue-600",viewBox:"0 0 24 24",fill:"none",children:[l.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),l.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8v8h8a8 8 0 01-8 8 8 8 0 01-8-8z"})]})}):l.jsx("div",{className:"overflow-x-auto",children:l.jsxs("table",{className:"w-full border-collapse",children:[l.jsx("thead",{children:l.jsx("tr",{className:"border-b bg-gray-50",children:["ID","Name","Occupation","Address","Phone"].map(v=>l.jsx("th",{className:"px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider",children:v},v))})}),l.jsx("tbody",{children:ae.map(v=>l.jsxs("tr",{className:"border-b transition-colors hover:bg-gray-50",children:[l.jsx("td",{className:"px-6 py-4 text-sm text-gray-900",children:v.id}),l.jsxs("td",{className:"px-6 py-4 text-sm font-medium text-gray-900",children:[v.first_name," ",v.last_name]}),l.jsx("td",{className:"px-6 py-4 text-sm text-gray-600",children:v.occupation}),l.jsx("td",{className:"px-6 py-4 text-sm text-gray-600",children:v.address}),l.jsx("td",{className:"px-6 py-4 text-sm text-gray-600",children:v.phone})]},v.id))})]})})]})]})]})})},jx=()=>{const[e,t]=j.useState("teachers"),[r,n]=j.useState([]),[s,o]=j.useState(!1),[i,a]=j.useState(""),[u,c]=j.useState([]),[f,d]=j.useState(!1),[g,b]=j.useState([]),[x,y]=j.useState(!1),[S,m]=j.useState([]),[p,h]=j.useState(!1),[N,w]=j.useState({username:"",password:"",email:"",role:"Teacher",course:"",is_admin:!1}),[_,T]=j.useState({user_id:"",first_name:"",last_name:"",date_of_birth:"",address:"",phone:""}),[R,D]=j.useState({user_id:"",first_name:"",last_name:"",occupation:"",address:"",phone:""}),[F,ae]=j.useState({name:"",description:""}),[Se,ve]=j.useState({student_id:"",course_id:""}),[X,we]=j.useState({parent_id:"",student_id:"",relationship:""}),[se,oe]=j.useState([]),L=async()=>{try{o(!0),a("");const E=await vt.getUsers();e==="teachers"&&n((E.users||[]).filter(U=>U.role==="Teacher"))}catch(E){a(E.error||"Failed to load teachers")}finally{o(!1)}},M=async()=>{try{d(!0),a("");const E=await Pe.getStudents();c(E.students||[])}catch(E){a(E.error||"Failed to load students")}finally{d(!1)}},A=async()=>{try{y(!0),a("");const E=await ct.getCourses();b(E.courses||[])}catch(E){a(E.error||"Failed to load courses")}finally{y(!1)}},V=async()=>{try{h(!0),a("");const E=await jr.getParents();m(E.parents||[])}catch(E){a(E.error||"Failed to load parents")}finally{h(!1)}},H=E=>{const{name:U,value:ue,type:mt,checked:ln}=E.target;w({...N,[U]:mt==="checkbox"?ln:ue})},he=E=>{const{name:U,value:ue}=E.target;T({..._,[U]:ue})},ge=E=>{const{name:U,value:ue}=E.target;D({...R,[U]:ue})},rt=E=>{const{name:U,value:ue}=E.target;ae({...F,[U]:ue})},Te=E=>{const{name:U,value:ue}=E.target;ve({...Se,[U]:ue})},k=async E=>{const{name:U,value:ue}=E.target;if(we({...X,[U]:ue}),U==="student_id"&&ue)try{o(!0);const mt=await Pe.getStudentParents(ue);if(oe(mt.parents||[]),mt.parents&&mt.parents.length>0){const ln=mt.parents.map(an=>`${an.first_name} ${an.last_name} (${an.relationship})`).join(", ");a(`Note: This student already has the following parents mapped: ${ln}`)}else a("")}catch(mt){console.error("Error fetching student parents:",mt)}finally{o(!1)}},B=async E=>{E.preventDefault();try{o(!0),a("");const U=I.getSchoolCode();if(!U){a("School code not found. Please contact super admin."),o(!1);return}await vt.registerUser({...N,role:"Teacher",main_code:U}),w({username:"",password:"",email:"",role:"Teacher",course:"",is_admin:!1}),a("Teacher registered successfully"),await L()}catch(U){a(U.error||"Failed to register teacher")}finally{o(!1)}},fe=async E=>{E.preventDefault();try{o(!0),a("");const U=I.getSchoolCode();if(!U){a("School code not found. Please contact super admin."),o(!1);return}const ue=await vt.registerUser({...N,role:"Student",main_code:U});await Pe.registerStudent({..._,user_id:ue.user.id,main_code:U}),w({username:"",password:"",email:"",role:"Student"}),T({user_id:"",first_name:"",last_name:"",date_of_birth:"",address:"",phone:""}),a("Student registered successfully"),await M()}catch(U){a(U.error||"Failed to register student")}finally{o(!1)}},pt=async E=>{E.preventDefault();try{o(!0),a("");const U=I.getSchoolCode();if(!U){a("School code not found. Please contact super admin."),o(!1);return}const ue=await vt.registerUser({...N,role:"Parent",main_code:U});await jr.registerParent({...R,user_id:ue.user.id,main_code:U}),w({username:"",password:"",email:"",role:"Parent"}),D({user_id:"",first_name:"",last_name:"",occupation:"",address:"",phone:""}),a("Parent registered successfully"),await V()}catch(U){a(U.error||"Failed to register parent")}finally{o(!1)}},Ot=async E=>{E.preventDefault();try{o(!0),a("");const U=I.getSchoolCode();if(!U){a("School code not found. Please contact super admin."),o(!1);return}await ct.createCourse({...F,main_code:U}),ae({name:"",description:""}),a("Course created successfully"),await A()}catch(U){a(U.error||"Failed to create course")}finally{o(!1)}},Mt=async E=>{E.preventDefault();try{o(!0),a(""),await ct.mapStudentToCourse(Se.student_id,Se.course_id),ve({student_id:"",course_id:""}),a("Student mapped to course successfully")}catch(U){a(U.error||"Failed to map student to course")}finally{o(!1)}},ls=async E=>{E.preventDefault();try{o(!0),a(""),await Pe.mapParentToStudent(X.parent_id,X.student_id,X.relationship),we({parent_id:"",student_id:"",relationship:""}),oe([]),a("Parent mapped to student successfully")}catch(U){a(U.error||"Failed to map parent to student")}finally{o(!1)}};return j.useEffect(()=>{e==="teachers"?L():e==="students"?M():e==="courses"?(A(),M()):e==="courseCreation"?A():e==="parents"?V():e==="parentMapping"&&(V(),M())},[e]),l.jsxs(os,{title:"Admin Dashboard",children:[l.jsx("div",{className:"mb-6",children:l.jsxs("div",{className:"flex flex-wrap border-b bg-gradient-to-r from-blue-100 to-purple-100 rounded-t-lg shadow-md",children:[l.jsx("button",{className:`py-3 px-5 font-semibold transition-all duration-300 ${e==="teachers"?"border-b-2 border-indigo-600 text-indigo-600 bg-white rounded-t-lg shadow-inner":"text-gray-600 hover:text-indigo-500"}`,onClick:()=>t("teachers"),children:"Teacher Registration"}),l.jsx("button",{className:`py-3 px-5 font-semibold transition-all duration-300 ${e==="students"?"border-b-2 border-indigo-600 text-indigo-600 bg-white rounded-t-lg shadow-inner":"text-gray-600 hover:text-indigo-500"}`,onClick:()=>t("students"),children:"Student Registration"}),l.jsx("button",{className:`py-3 px-5 font-semibold transition-all duration-300 ${e==="courseCreation"?"border-b-2 border-indigo-600 text-indigo-600 bg-white rounded-t-lg shadow-inner":"text-gray-600 hover:text-indigo-500"}`,onClick:()=>t("courseCreation"),children:"Create Course"}),l.jsx("button",{className:`py-3 px-5 font-semibold transition-all duration-300 ${e==="courses"?"border-b-2 border-indigo-600 text-indigo-600 bg-white rounded-t-lg shadow-inner":"text-gray-600 hover:text-indigo-500"}`,onClick:()=>t("courses"),children:"Course Mapping"}),l.jsx("button",{className:`py-3 px-5 font-semibold transition-all duration-300 ${e==="parents"?"border-b-2 border-indigo-600 text-indigo-600 bg-white rounded-t-lg shadow-inner":"text-gray-600 hover:text-indigo-500"}`,onClick:()=>t("parents"),children:"Parent Registration"}),l.jsx("button",{className:`py-3 px-5 font-semibold transition-all duration-300 ${e==="parentMapping"?"border-b-2 border-indigo-600 text-indigo-600 bg-white rounded-t-lg shadow-inner":"text-gray-600 hover:text-indigo-500"}`,onClick:()=>t("parentMapping"),children:"Parent Mapping"})]})}),e==="teachers"&&l.jsxs("div",{children:[l.jsxs("div",{className:"card mb-6 bg-white rounded-lg shadow-lg p-6 border-t-4 border-green-500",children:[l.jsx("h2",{className:"text-2xl font-bold mb-4 text-green-700",children:"Register New Teacher"}),i&&l.jsx("div",{className:`p-4 mb-4 rounded-md ${i.includes("successfully")?"bg-green-100 text-green-700":"bg-red-100 text-red-700"}`,role:"alert",children:i}),l.jsxs("form",{onSubmit:B,children:[l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"username",className:"form-label text-gray-700 font-medium",children:"Username"}),l.jsx("input",{type:"text",className:"form-input w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring focus:ring-green-200 focus:ring-opacity-50",id:"username",name:"username",value:N.username,onChange:H,required:!0})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"password",className:"form-label text-gray-700 font-medium",children:"Password"}),l.jsx("input",{type:"password",className:"form-input w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring focus:ring-green-200 focus:ring-opacity-50",id:"password",name:"password",value:N.password,onChange:H,required:!0})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"email",className:"form-label text-gray-700 font-medium",children:"Email"}),l.jsx("input",{type:"email",className:"form-input w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring focus:ring-green-200 focus:ring-opacity-50",id:"email",name:"email",value:N.email,onChange:H,required:!0})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"course",className:"form-label text-gray-700 font-medium",children:"Course"}),l.jsxs("select",{className:"form-input w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring focus:ring-green-200 focus:ring-opacity-50",id:"course",name:"course",value:N.course,onChange:H,children:[l.jsx("option",{value:"",children:"Select Course"}),l.jsx("option",{value:"Neet",children:"Neet"}),l.jsx("option",{value:"Jee",children:"Jee"})]})]}),l.jsx("div",{className:"form-group",children:l.jsxs("label",{className:"inline-flex items-center mt-4",children:[l.jsx("input",{type:"checkbox",className:"form-checkbox rounded text-green-600 focus:ring-green-500",id:"is_admin",name:"is_admin",checked:N.is_admin,onChange:H}),l.jsx("span",{className:"ml-2 text-gray-700",children:"Is Admin"})]})})]}),l.jsx("button",{type:"submit",className:"mt-4 px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50 transition-colors duration-300",disabled:s,children:s?"Registering...":"Register Teacher"})]})]}),l.jsxs("div",{className:"card bg-white rounded-lg shadow-lg p-6",children:[l.jsx("h2",{className:"text-2xl font-bold mb-4 text-green-700",children:"All Teachers"}),s?l.jsx("p",{className:"text-gray-600",children:"Loading teachers..."}):l.jsx("div",{className:"overflow-x-auto",children:l.jsxs("table",{className:"min-w-full bg-white border border-gray-200 rounded-lg overflow-hidden",children:[l.jsx("thead",{className:"bg-gray-50",children:l.jsxs("tr",{children:[l.jsx("th",{className:"py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"ID"}),l.jsx("th",{className:"py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Username"}),l.jsx("th",{className:"py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Email"}),l.jsx("th",{className:"py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Is Admin"}),l.jsx("th",{className:"py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Course"}),l.jsx("th",{className:"py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Main Code"})]})}),l.jsx("tbody",{className:"divide-y divide-gray-200",children:r.map(E=>l.jsxs("tr",{className:"hover:bg-gray-50",children:[l.jsx("td",{className:"py-3 px-4 text-sm text-gray-900",children:E.id}),l.jsx("td",{className:"py-3 px-4 text-sm text-gray-900 font-medium",children:E.username}),l.jsx("td",{className:"py-3 px-4 text-sm text-gray-500",children:E.email}),l.jsx("td",{className:"py-3 px-4 text-sm text-gray-500",children:E.is_admin?l.jsx("span",{className:"px-2 py-1 rounded-full bg-green-100 text-green-800 text-xs font-medium",children:"Yes"}):l.jsx("span",{className:"px-2 py-1 rounded-full bg-gray-100 text-gray-800 text-xs font-medium",children:"No"})}),l.jsx("td",{className:"py-3 px-4 text-sm text-gray-500",children:E.course?l.jsx("span",{className:"px-2 py-1 rounded-full bg-blue-100 text-blue-800 text-xs font-medium",children:E.course}):l.jsx("span",{className:"px-2 py-1 rounded-full bg-gray-100 text-gray-800 text-xs font-medium",children:"None"})}),l.jsx("td",{className:"py-3 px-4 text-sm text-gray-500",children:E.main_code||"-"})]},E.id))})]})})]})]}),e==="students"&&l.jsxs("div",{children:[l.jsxs("div",{className:"card mb-6 bg-white rounded-lg shadow-lg p-6 border-t-4 border-purple-500",children:[l.jsx("h2",{className:"text-2xl font-bold mb-4 text-purple-700",children:"Register New Student"}),i&&l.jsx("div",{className:`p-4 mb-4 rounded-md ${i.includes("successfully")?"bg-green-100 text-green-700":"bg-red-100 text-red-700"}`,role:"alert",children:i}),l.jsxs("form",{onSubmit:fe,children:[l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"username",className:"form-label text-gray-700 font-medium",children:"Username"}),l.jsx("input",{type:"text",className:"form-input w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring focus:ring-purple-200 focus:ring-opacity-50",id:"username",name:"username",value:N.username,onChange:H,required:!0})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"password",className:"form-label text-gray-700 font-medium",children:"Password"}),l.jsx("input",{type:"password",className:"form-input w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring focus:ring-purple-200 focus:ring-opacity-50",id:"password",name:"password",value:N.password,onChange:H,required:!0})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"email",className:"form-label text-gray-700 font-medium",children:"Email"}),l.jsx("input",{type:"email",className:"form-input w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring focus:ring-purple-200 focus:ring-opacity-50",id:"email",name:"email",value:N.email,onChange:H,required:!0})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"first_name",className:"form-label text-gray-700 font-medium",children:"First Name"}),l.jsx("input",{type:"text",className:"form-input w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring focus:ring-purple-200 focus:ring-opacity-50",id:"first_name",name:"first_name",value:_.first_name,onChange:he,required:!0})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"last_name",className:"form-label text-gray-700 font-medium",children:"Last Name"}),l.jsx("input",{type:"text",className:"form-input w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring focus:ring-purple-200 focus:ring-opacity-50",id:"last_name",name:"last_name",value:_.last_name,onChange:he,required:!0})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"date_of_birth",className:"form-label text-gray-700 font-medium",children:"Date of Birth"}),l.jsx("input",{type:"date",className:"form-input w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring focus:ring-purple-200 focus:ring-opacity-50",id:"date_of_birth",name:"date_of_birth",value:_.date_of_birth,onChange:he})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"address",className:"form-label text-gray-700 font-medium",children:"Address"}),l.jsx("input",{type:"text",className:"form-input w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring focus:ring-purple-200 focus:ring-opacity-50",id:"address",name:"address",value:_.address,onChange:he})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"phone",className:"form-label text-gray-700 font-medium",children:"Phone"}),l.jsx("input",{type:"text",className:"form-input w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring focus:ring-purple-200 focus:ring-opacity-50",id:"phone",name:"phone",value:_.phone,onChange:he})]})]}),l.jsx("button",{type:"submit",className:"mt-4 px-6 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-opacity-50 transition-colors duration-300",disabled:s,children:s?"Registering...":"Register Student"})]})]}),l.jsxs("div",{className:"card bg-white rounded-lg shadow-lg p-6",children:[l.jsx("h2",{className:"text-2xl font-bold mb-4 text-purple-700",children:"All Students"}),f?l.jsx("p",{className:"text-gray-600",children:"Loading students..."}):l.jsx("div",{className:"overflow-x-auto",children:l.jsxs("table",{className:"min-w-full bg-white border border-gray-200 rounded-lg overflow-hidden",children:[l.jsx("thead",{className:"bg-gray-50",children:l.jsxs("tr",{children:[l.jsx("th",{className:"py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"ID"}),l.jsx("th",{className:"py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User ID"}),l.jsx("th",{className:"py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"First Name"}),l.jsx("th",{className:"py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Last Name"}),l.jsx("th",{className:"py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date of Birth"}),l.jsx("th",{className:"py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Phone"})]})}),l.jsx("tbody",{className:"divide-y divide-gray-200",children:u.map(E=>l.jsxs("tr",{className:"hover:bg-gray-50",children:[l.jsx("td",{className:"py-3 px-4 text-sm text-gray-900",children:E.id}),l.jsx("td",{className:"py-3 px-4 text-sm text-gray-900",children:E.user_id}),l.jsx("td",{className:"py-3 px-4 text-sm text-gray-900 font-medium",children:E.first_name}),l.jsx("td",{className:"py-3 px-4 text-sm text-gray-900 font-medium",children:E.last_name}),l.jsx("td",{className:"py-3 px-4 text-sm text-gray-500",children:E.date_of_birth}),l.jsx("td",{className:"py-3 px-4 text-sm text-gray-500",children:E.phone})]},E.id))})]})})]})]}),e==="courseCreation"&&l.jsxs("div",{children:[l.jsxs("div",{className:"card mb-6 bg-white rounded-lg shadow-lg p-6 border-t-4 border-indigo-500",children:[l.jsx("h2",{className:"text-2xl font-bold mb-4 text-indigo-700",children:"Create New Course"}),i&&l.jsx("div",{className:`p-4 mb-4 rounded-md ${i.includes("successfully")?"bg-green-100 text-green-700":"bg-red-100 text-red-700"}`,role:"alert",children:i}),l.jsxs("form",{onSubmit:Ot,children:[l.jsxs("div",{className:"grid grid-cols-1 gap-4",children:[l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"name",className:"form-label text-gray-700 font-medium",children:"Course Name"}),l.jsx("input",{type:"text",className:"form-input w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50",id:"name",name:"name",value:F.name,onChange:rt,required:!0})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"description",className:"form-label text-gray-700 font-medium",children:"Description"}),l.jsx("textarea",{className:"form-input w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50",id:"description",name:"description",value:F.description,onChange:rt,rows:"3"})]})]}),l.jsx("button",{type:"submit",className:"mt-4 px-6 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-opacity-50 transition-colors duration-300",disabled:s,children:s?"Creating...":"Create Course"})]})]}),l.jsxs("div",{className:"card bg-white rounded-lg shadow-lg p-6",children:[l.jsx("h2",{className:"text-2xl font-bold mb-4 text-indigo-700",children:"All Courses"}),x?l.jsx("p",{className:"text-gray-600",children:"Loading courses..."}):l.jsx("div",{className:"overflow-x-auto",children:l.jsxs("table",{className:"min-w-full bg-white border border-gray-200 rounded-lg overflow-hidden",children:[l.jsx("thead",{className:"bg-gray-50",children:l.jsxs("tr",{children:[l.jsx("th",{className:"py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"ID"}),l.jsx("th",{className:"py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Name"}),l.jsx("th",{className:"py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Description"})]})}),l.jsx("tbody",{className:"divide-y divide-gray-200",children:g.map(E=>l.jsxs("tr",{className:"hover:bg-gray-50",children:[l.jsx("td",{className:"py-3 px-4 text-sm text-gray-900",children:E.id}),l.jsx("td",{className:"py-3 px-4 text-sm text-gray-900 font-medium",children:E.name}),l.jsx("td",{className:"py-3 px-4 text-sm text-gray-500",children:E.description})]},E.id))})]})})]})]}),e==="courses"&&l.jsxs("div",{children:[l.jsxs("div",{className:"card mb-6 bg-white rounded-lg shadow-lg p-6 border-t-4 border-purple-500",children:[l.jsx("h2",{className:"text-2xl font-bold mb-4 text-purple-700",children:"Map Student to Course"}),i&&l.jsx("div",{className:`p-4 mb-4 rounded-md ${i.includes("successfully")?"bg-green-100 text-green-700":"bg-red-100 text-red-700"}`,role:"alert",children:i}),l.jsxs("form",{onSubmit:Mt,children:[l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"student_id",className:"form-label text-gray-700 font-medium",children:"Student"}),l.jsxs("select",{className:"form-input w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring focus:ring-purple-200 focus:ring-opacity-50",id:"student_id",name:"student_id",value:Se.student_id,onChange:Te,required:!0,children:[l.jsx("option",{value:"",children:"Select Student"}),u.map(E=>l.jsxs("option",{value:E.id,children:[E.first_name," ",E.last_name]},E.id))]})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"course_id",className:"form-label text-gray-700 font-medium",children:"Course"}),l.jsxs("select",{className:"form-input w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring focus:ring-purple-200 focus:ring-opacity-50",id:"course_id",name:"course_id",value:Se.course_id,onChange:Te,required:!0,children:[l.jsx("option",{value:"",children:"Select Course"}),g.map(E=>l.jsx("option",{value:E.id,children:E.name},E.id))]})]})]}),l.jsx("button",{type:"submit",className:"mt-4 px-6 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-opacity-50 transition-colors duration-300",disabled:s,children:s?"Mapping...":"Map Student to Course"})]})]}),l.jsxs("div",{className:"card bg-white rounded-lg shadow-lg p-6",children:[l.jsx("h2",{className:"text-2xl font-bold mb-4 text-purple-700",children:"All Courses"}),x?l.jsx("p",{className:"text-gray-600",children:"Loading courses..."}):l.jsx("div",{className:"overflow-x-auto",children:l.jsxs("table",{className:"min-w-full bg-white border border-gray-200 rounded-lg overflow-hidden",children:[l.jsx("thead",{className:"bg-gray-50",children:l.jsxs("tr",{children:[l.jsx("th",{className:"py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"ID"}),l.jsx("th",{className:"py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Name"}),l.jsx("th",{className:"py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Description"})]})}),l.jsx("tbody",{className:"divide-y divide-gray-200",children:g.map(E=>l.jsxs("tr",{className:"hover:bg-gray-50",children:[l.jsx("td",{className:"py-3 px-4 text-sm text-gray-900",children:E.id}),l.jsx("td",{className:"py-3 px-4 text-sm text-gray-900 font-medium",children:E.name}),l.jsx("td",{className:"py-3 px-4 text-sm text-gray-500",children:E.description})]},E.id))})]})})]})]}),e==="parents"&&l.jsxs("div",{children:[l.jsxs("div",{className:"card mb-6 bg-white rounded-lg shadow-lg p-6 border-t-4 border-teal-500",children:[l.jsx("h2",{className:"text-2xl font-bold mb-4 text-teal-700",children:"Register New Parent"}),i&&l.jsx("div",{className:`p-4 mb-4 rounded-md ${i.includes("successfully")?"bg-green-100 text-green-700":"bg-red-100 text-red-700"}`,role:"alert",children:i}),l.jsxs("form",{onSubmit:pt,children:[l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"username",className:"form-label text-gray-700 font-medium",children:"Username"}),l.jsx("input",{type:"text",className:"form-input w-full rounded-md border-gray-300 shadow-sm focus:border-teal-500 focus:ring focus:ring-teal-200 focus:ring-opacity-50",id:"username",name:"username",value:N.username,onChange:H,required:!0})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"password",className:"form-label text-gray-700 font-medium",children:"Password"}),l.jsx("input",{type:"password",className:"form-input w-full rounded-md border-gray-300 shadow-sm focus:border-teal-500 focus:ring focus:ring-teal-200 focus:ring-opacity-50",id:"password",name:"password",value:N.password,onChange:H,required:!0})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"email",className:"form-label text-gray-700 font-medium",children:"Email"}),l.jsx("input",{type:"email",className:"form-input w-full rounded-md border-gray-300 shadow-sm focus:border-teal-500 focus:ring focus:ring-teal-200 focus:ring-opacity-50",id:"email",name:"email",value:N.email,onChange:H,required:!0})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"first_name",className:"form-label text-gray-700 font-medium",children:"First Name"}),l.jsx("input",{type:"text",className:"form-input w-full rounded-md border-gray-300 shadow-sm focus:border-teal-500 focus:ring focus:ring-teal-200 focus:ring-opacity-50",id:"first_name",name:"first_name",value:R.first_name,onChange:ge,required:!0})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"last_name",className:"form-label text-gray-700 font-medium",children:"Last Name"}),l.jsx("input",{type:"text",className:"form-input w-full rounded-md border-gray-300 shadow-sm focus:border-teal-500 focus:ring focus:ring-teal-200 focus:ring-opacity-50",id:"last_name",name:"last_name",value:R.last_name,onChange:ge,required:!0})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"occupation",className:"form-label text-gray-700 font-medium",children:"Occupation"}),l.jsx("input",{type:"text",className:"form-input w-full rounded-md border-gray-300 shadow-sm focus:border-teal-500 focus:ring focus:ring-teal-200 focus:ring-opacity-50",id:"occupation",name:"occupation",value:R.occupation,onChange:ge})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"address",className:"form-label text-gray-700 font-medium",children:"Address"}),l.jsx("input",{type:"text",className:"form-input w-full rounded-md border-gray-300 shadow-sm focus:border-teal-500 focus:ring focus:ring-teal-200 focus:ring-opacity-50",id:"address",name:"address",value:R.address,onChange:ge})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"phone",className:"form-label text-gray-700 font-medium",children:"Phone"}),l.jsx("input",{type:"text",className:"form-input w-full rounded-md border-gray-300 shadow-sm focus:border-teal-500 focus:ring focus:ring-teal-200 focus:ring-opacity-50",id:"phone",name:"phone",value:R.phone,onChange:ge})]})]}),l.jsx("button",{type:"submit",className:"mt-4 px-6 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-opacity-50 transition-colors duration-300",disabled:s,children:s?"Registering...":"Register Parent"})]})]}),l.jsxs("div",{className:"card bg-white rounded-lg shadow-lg p-6",children:[l.jsx("h2",{className:"text-2xl font-bold mb-4 text-teal-700",children:"All Parents"}),p?l.jsx("p",{className:"text-gray-600",children:"Loading parents..."}):l.jsx("div",{className:"overflow-x-auto",children:l.jsxs("table",{className:"min-w-full bg-white border border-gray-200 rounded-lg overflow-hidden",children:[l.jsx("thead",{className:"bg-gray-50",children:l.jsxs("tr",{children:[l.jsx("th",{className:"py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"ID"}),l.jsx("th",{className:"py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User ID"}),l.jsx("th",{className:"py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"First Name"}),l.jsx("th",{className:"py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Last Name"}),l.jsx("th",{className:"py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Phone"})]})}),l.jsx("tbody",{className:"divide-y divide-gray-200",children:S.map(E=>l.jsxs("tr",{className:"hover:bg-gray-50",children:[l.jsx("td",{className:"py-3 px-4 text-sm text-gray-900",children:E.id}),l.jsx("td",{className:"py-3 px-4 text-sm text-gray-900",children:E.user_id}),l.jsx("td",{className:"py-3 px-4 text-sm text-gray-900 font-medium",children:E.first_name}),l.jsx("td",{className:"py-3 px-4 text-sm text-gray-900 font-medium",children:E.last_name}),l.jsx("td",{className:"py-3 px-4 text-sm text-gray-500",children:E.phone})]},E.id))})]})})]})]}),e==="parentMapping"&&l.jsx("div",{children:l.jsxs("div",{className:"card mb-6 bg-white rounded-lg shadow-lg p-6 border-t-4 border-amber-500",children:[l.jsx("h2",{className:"text-2xl font-bold mb-4 text-amber-700",children:"Map Parent to Student"}),i&&l.jsx("div",{className:`p-4 mb-4 rounded-md ${i.includes("successfully")?"bg-green-100 text-green-700":i.includes("Note:")?"bg-blue-100 text-blue-700":"bg-red-100 text-red-700"}`,role:"alert",children:i}),l.jsxs("form",{onSubmit:ls,children:[l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"student_id",className:"form-label text-gray-700 font-medium",children:"Student"}),l.jsxs("select",{className:"form-input w-full rounded-md border-gray-300 shadow-sm focus:border-amber-500 focus:ring focus:ring-amber-200 focus:ring-opacity-50",id:"student_id",name:"student_id",value:X.student_id,onChange:k,required:!0,children:[l.jsx("option",{value:"",children:"Select Student"}),u.map(E=>l.jsxs("option",{value:E.id,children:[E.first_name," ",E.last_name]},E.id))]})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"parent_id",className:"form-label text-gray-700 font-medium",children:"Parent"}),l.jsxs("select",{className:"form-input w-full rounded-md border-gray-300 shadow-sm focus:border-amber-500 focus:ring focus:ring-amber-200 focus:ring-opacity-50",id:"parent_id",name:"parent_id",value:X.parent_id,onChange:k,required:!0,children:[l.jsx("option",{value:"",children:"Select Parent"}),S.map(E=>l.jsxs("option",{value:E.id,children:[E.first_name," ",E.last_name]},E.id))]})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"relationship",className:"form-label text-gray-700 font-medium",children:"Relationship"}),l.jsxs("select",{className:"form-input w-full rounded-md border-gray-300 shadow-sm focus:border-amber-500 focus:ring focus:ring-amber-200 focus:ring-opacity-50",id:"relationship",name:"relationship",value:X.relationship,onChange:k,required:!0,children:[l.jsx("option",{value:"",children:"Select Relationship"}),l.jsx("option",{value:"Father",children:"Father"}),l.jsx("option",{value:"Mother",children:"Mother"}),l.jsx("option",{value:"Guardian",children:"Guardian"})]})]})]}),se.length>0&&l.jsxs("div",{className:"mt-4 p-4 bg-blue-50 rounded-md border border-blue-200",children:[l.jsx("h3",{className:"text-lg font-semibold text-blue-700 mb-2",children:"Existing Parents for Selected Student"}),l.jsx("ul",{className:"list-disc pl-5 space-y-1",children:se.map(E=>l.jsxs("li",{className:"text-blue-600",children:[E.first_name," ",E.last_name," - ",l.jsx("span",{className:"font-medium",children:E.relationship})]},E.id))})]}),l.jsx("button",{type:"submit",className:"mt-4 px-6 py-2 bg-amber-600 text-white rounded-md hover:bg-amber-700 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-opacity-50 transition-colors duration-300",disabled:s,children:s?"Mapping...":"Map Parent to Student"})]})]})})]})},bx=()=>{const[e,t]=j.useState("students"),[r,n]=j.useState([]),[s,o]=j.useState(!1),[i,a]=j.useState(""),[u,c]=j.useState([]),[f,d]=j.useState(!1),[g,b]=j.useState([]),[x,y]=j.useState(!1),[S,m]=j.useState({username:"",password:"",email:"",role:"Student"}),[p,h]=j.useState({user_id:"",first_name:"",last_name:"",date_of_birth:"",address:"",phone:""}),[N,w]=j.useState({user_id:"",first_name:"",last_name:"",occupation:"",address:"",phone:""}),[_,T]=j.useState({name:"",description:""}),[R,D]=j.useState({student_id:"",course_id:""}),[F,ae]=j.useState({parent_id:"",student_id:"",relationship:""}),[Se,ve]=j.useState([]),X=async()=>{try{o(!0),a("");const k=await Pe.getStudents();n(k.students||[])}catch(k){a(k.error||"Failed to load students")}finally{o(!1)}},we=async()=>{try{d(!0);const k=await ct.getCourses();c(k.courses||[])}catch(k){console.error("Failed to load courses:",k)}finally{d(!1)}},se=async()=>{try{y(!0);const k=await jr.getParents();b(k.parents||[])}catch(k){console.error("Failed to load parents:",k)}finally{y(!1)}},oe=k=>{const{name:B,value:fe}=k.target;m({...S,[B]:fe})},L=k=>{const{name:B,value:fe}=k.target;h({...p,[B]:fe})},M=k=>{const{name:B,value:fe}=k.target;T({..._,[B]:fe})},A=k=>{const{name:B,value:fe}=k.target;D({...R,[B]:fe})},V=async k=>{const{name:B,value:fe}=k.target;if(ae({...F,[B]:fe}),B==="student_id"&&fe)try{o(!0);const pt=await Pe.getStudentParents(fe);if(ve(pt.parents||[]),pt.parents&&pt.parents.length>0){const Ot=pt.parents.map(Mt=>`${Mt.first_name} ${Mt.last_name} (${Mt.relationship})`).join(", ");a(`Note: This student already has the following parents mapped: ${Ot}`)}else a("")}catch(pt){console.error("Error fetching student parents:",pt)}finally{o(!1)}},H=async k=>{k.preventDefault();try{o(!0),a("");const B=I.getSchoolCode();if(!B){a("School code not found. Please contact administrator."),o(!1);return}const fe=await vt.registerUser({...S,role:"Student",main_code:B});await Pe.registerStudent({...p,user_id:fe.user.id,main_code:B}),m({username:"",password:"",email:"",role:"Student"}),h({user_id:"",first_name:"",last_name:"",date_of_birth:"",address:"",phone:""}),a("Student registered successfully"),await X()}catch(B){a(B.error||"Failed to register student")}finally{o(!1)}},he=async k=>{k.preventDefault();try{o(!0),a("");const B=I.getSchoolCode();if(!B){a("School code not found. Please contact administrator."),o(!1);return}const fe=await vt.registerUser({...S,role:"Parent",main_code:B});await jr.registerParent({...N,user_id:fe.user.id,main_code:B}),m({username:"",password:"",email:"",role:"Parent"}),w({user_id:"",first_name:"",last_name:"",occupation:"",address:"",phone:""}),a("Parent registered successfully"),await se()}catch(B){a(B.error||"Failed to register parent")}finally{o(!1)}},ge=async k=>{k.preventDefault();try{o(!0),a("");const B=I.getSchoolCode();if(!B){a("School code not found. Please contact administrator."),o(!1);return}await ct.createCourse({..._,main_code:B}),T({name:"",description:""}),a("Course created successfully"),await we()}catch(B){a(B.error||"Failed to create course")}finally{o(!1)}},rt=async k=>{k.preventDefault();try{o(!0),a(""),await ct.mapStudentToCourse(R.student_id,R.course_id),D({student_id:"",course_id:""}),a("Student mapped to course successfully")}catch(B){a(B.error||"Failed to map student to course")}finally{o(!1)}},Te=async k=>{k.preventDefault();try{o(!0),a(""),await Pe.mapParentToStudent(F.parent_id,F.student_id,F.relationship),ae({parent_id:"",student_id:"",relationship:""}),ve([]),a("Parent mapped to student successfully")}catch(B){a(B.error||"Failed to map parent to student")}finally{o(!1)}};return j.useEffect(()=>{e==="students"?X():e==="courses"?(we(),X()):e==="courseCreation"?we():e==="parents"?se():e==="parentMapping"&&(se(),X())},[e]),l.jsxs(os,{title:"Teacher Dashboard",children:[l.jsx("div",{className:"mb-6",children:l.jsxs("div",{className:"flex flex-wrap border-b bg-gradient-to-r from-blue-100 to-purple-100 rounded-t-lg shadow-md",children:[l.jsx("button",{className:`py-3 px-5 font-semibold transition-all duration-300 ${e==="students"?"border-b-2 border-indigo-600 text-indigo-600 bg-white rounded-t-lg shadow-inner":"text-gray-600 hover:text-indigo-500"}`,onClick:()=>t("students"),children:"Student Registration"}),l.jsx("button",{className:`py-3 px-5 font-semibold transition-all duration-300 ${e==="courseCreation"?"border-b-2 border-indigo-600 text-indigo-600 bg-white rounded-t-lg shadow-inner":"text-gray-600 hover:text-indigo-500"}`,onClick:()=>t("courseCreation"),children:"Create Course"}),l.jsx("button",{className:`py-3 px-5 font-semibold transition-all duration-300 ${e==="courses"?"border-b-2 border-indigo-600 text-indigo-600 bg-white rounded-t-lg shadow-inner":"text-gray-600 hover:text-indigo-500"}`,onClick:()=>t("courses"),children:"Course Mapping"}),l.jsx("button",{className:`py-3 px-5 font-semibold transition-all duration-300 ${e==="parents"?"border-b-2 border-indigo-600 text-indigo-600 bg-white rounded-t-lg shadow-inner":"text-gray-600 hover:text-indigo-500"}`,onClick:()=>t("parents"),children:"Parent Registration"}),l.jsx("button",{className:`py-3 px-5 font-semibold transition-all duration-300 ${e==="parentMapping"?"border-b-2 border-indigo-600 text-indigo-600 bg-white rounded-t-lg shadow-inner":"text-gray-600 hover:text-indigo-500"}`,onClick:()=>t("parentMapping"),children:"Parent Mapping"})]})}),e==="students"&&l.jsxs("div",{children:[l.jsxs("div",{className:"card mb-6",children:[l.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Register New Student"}),i&&l.jsx("div",{className:`alert ${i.includes("successfully")?"alert-success":"alert-danger"}`,role:"alert",children:i}),l.jsxs("form",{onSubmit:H,children:[l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"username",className:"form-label",children:"Username"}),l.jsx("input",{type:"text",className:"form-input",id:"username",name:"username",value:S.username,onChange:oe,required:!0})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"password",className:"form-label",children:"Password"}),l.jsx("input",{type:"password",className:"form-input",id:"password",name:"password",value:S.password,onChange:oe,required:!0})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"email",className:"form-label",children:"Email"}),l.jsx("input",{type:"email",className:"form-input",id:"email",name:"email",value:S.email,onChange:oe,required:!0})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"first_name",className:"form-label",children:"First Name"}),l.jsx("input",{type:"text",className:"form-input",id:"first_name",name:"first_name",value:p.first_name,onChange:L,required:!0})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"last_name",className:"form-label",children:"Last Name"}),l.jsx("input",{type:"text",className:"form-input",id:"last_name",name:"last_name",value:p.last_name,onChange:L,required:!0})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"date_of_birth",className:"form-label",children:"Date of Birth"}),l.jsx("input",{type:"date",className:"form-input",id:"date_of_birth",name:"date_of_birth",value:p.date_of_birth,onChange:L})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"address",className:"form-label",children:"Address"}),l.jsx("input",{type:"text",className:"form-input",id:"address",name:"address",value:p.address,onChange:L})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"phone",className:"form-label",children:"Phone"}),l.jsx("input",{type:"text",className:"form-input",id:"phone",name:"phone",value:p.phone,onChange:L})]})]}),l.jsx("button",{type:"submit",className:"btn btn-primary mt-4",disabled:s,children:s?"Registering...":"Register Student"})]})]}),l.jsxs("div",{className:"card",children:[l.jsx("h2",{className:"text-xl font-semibold mb-4",children:"All Students"}),s?l.jsx("p",{children:"Loading students..."}):l.jsx("div",{className:"overflow-x-auto",children:l.jsxs("table",{className:"min-w-full bg-white",children:[l.jsx("thead",{children:l.jsxs("tr",{children:[l.jsx("th",{className:"py-2 px-4 border-b",children:"ID"}),l.jsx("th",{className:"py-2 px-4 border-b",children:"User ID"}),l.jsx("th",{className:"py-2 px-4 border-b",children:"First Name"}),l.jsx("th",{className:"py-2 px-4 border-b",children:"Last Name"}),l.jsx("th",{className:"py-2 px-4 border-b",children:"Date of Birth"}),l.jsx("th",{className:"py-2 px-4 border-b",children:"Phone"})]})}),l.jsx("tbody",{children:r.map(k=>l.jsxs("tr",{children:[l.jsx("td",{className:"py-2 px-4 border-b",children:k.id}),l.jsx("td",{className:"py-2 px-4 border-b",children:k.user_id}),l.jsx("td",{className:"py-2 px-4 border-b",children:k.first_name}),l.jsx("td",{className:"py-2 px-4 border-b",children:k.last_name}),l.jsx("td",{className:"py-2 px-4 border-b",children:k.date_of_birth}),l.jsx("td",{className:"py-2 px-4 border-b",children:k.phone})]},k.id))})]})})]})]}),e==="courseCreation"&&l.jsxs("div",{children:[l.jsxs("div",{className:"card mb-6 bg-white rounded-lg shadow-lg p-6 border-t-4 border-indigo-500",children:[l.jsx("h2",{className:"text-2xl font-bold mb-4 text-indigo-700",children:"Create New Course"}),i&&l.jsx("div",{className:`p-4 mb-4 rounded-md ${i.includes("successfully")?"bg-green-100 text-green-700":"bg-red-100 text-red-700"}`,role:"alert",children:i}),l.jsxs("form",{onSubmit:ge,children:[l.jsxs("div",{className:"grid grid-cols-1 gap-4",children:[l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"name",className:"form-label text-gray-700 font-medium",children:"Course Name"}),l.jsx("input",{type:"text",className:"form-input w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50",id:"name",name:"name",value:_.name,onChange:M,required:!0})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"description",className:"form-label text-gray-700 font-medium",children:"Description"}),l.jsx("textarea",{className:"form-input w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50",id:"description",name:"description",value:_.description,onChange:M,rows:"3"})]})]}),l.jsx("button",{type:"submit",className:"mt-4 px-6 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-opacity-50 transition-colors duration-300",disabled:s,children:s?"Creating...":"Create Course"})]})]}),l.jsxs("div",{className:"card bg-white rounded-lg shadow-lg p-6",children:[l.jsx("h2",{className:"text-2xl font-bold mb-4 text-indigo-700",children:"All Courses"}),s?l.jsx("p",{className:"text-gray-600",children:"Loading courses..."}):l.jsx("div",{className:"overflow-x-auto",children:l.jsxs("table",{className:"min-w-full bg-white border border-gray-200 rounded-lg overflow-hidden",children:[l.jsx("thead",{className:"bg-gray-50",children:l.jsxs("tr",{children:[l.jsx("th",{className:"py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"ID"}),l.jsx("th",{className:"py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Name"}),l.jsx("th",{className:"py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Description"})]})}),l.jsx("tbody",{className:"divide-y divide-gray-200",children:u.map(k=>l.jsxs("tr",{className:"hover:bg-gray-50",children:[l.jsx("td",{className:"py-3 px-4 text-sm text-gray-900",children:k.id}),l.jsx("td",{className:"py-3 px-4 text-sm text-gray-900 font-medium",children:k.name}),l.jsx("td",{className:"py-3 px-4 text-sm text-gray-500",children:k.description})]},k.id))})]})})]})]}),e==="courses"&&l.jsxs("div",{children:[l.jsxs("div",{className:"card mb-6 bg-white rounded-lg shadow-lg p-6 border-t-4 border-purple-500",children:[l.jsx("h2",{className:"text-2xl font-bold mb-4 text-purple-700",children:"Map Student to Course"}),i&&l.jsx("div",{className:`p-4 mb-4 rounded-md ${i.includes("successfully")?"bg-green-100 text-green-700":"bg-red-100 text-red-700"}`,role:"alert",children:i}),l.jsxs("form",{onSubmit:rt,children:[l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"student_id",className:"form-label text-gray-700 font-medium",children:"Student"}),l.jsxs("select",{className:"form-input w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring focus:ring-purple-200 focus:ring-opacity-50",id:"student_id",name:"student_id",value:R.student_id,onChange:A,required:!0,children:[l.jsx("option",{value:"",children:"Select Student"}),r.map(k=>l.jsxs("option",{value:k.id,children:[k.first_name," ",k.last_name]},k.id))]})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"course_id",className:"form-label text-gray-700 font-medium",children:"Course"}),l.jsxs("select",{className:"form-input w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring focus:ring-purple-200 focus:ring-opacity-50",id:"course_id",name:"course_id",value:R.course_id,onChange:A,required:!0,children:[l.jsx("option",{value:"",children:"Select Course"}),u.map(k=>l.jsx("option",{value:k.id,children:k.name},k.id))]})]})]}),l.jsx("button",{type:"submit",className:"mt-4 px-6 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-opacity-50 transition-colors duration-300",disabled:s,children:s?"Mapping...":"Map Student to Course"})]})]}),l.jsxs("div",{className:"card bg-white rounded-lg shadow-lg p-6",children:[l.jsx("h2",{className:"text-2xl font-bold mb-4 text-purple-700",children:"All Courses"}),s?l.jsx("p",{className:"text-gray-600",children:"Loading courses..."}):l.jsx("div",{className:"overflow-x-auto",children:l.jsxs("table",{className:"min-w-full bg-white border border-gray-200 rounded-lg overflow-hidden",children:[l.jsx("thead",{className:"bg-gray-50",children:l.jsxs("tr",{children:[l.jsx("th",{className:"py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"ID"}),l.jsx("th",{className:"py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Name"}),l.jsx("th",{className:"py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Description"})]})}),l.jsx("tbody",{className:"divide-y divide-gray-200",children:u.map(k=>l.jsxs("tr",{className:"hover:bg-gray-50",children:[l.jsx("td",{className:"py-3 px-4 text-sm text-gray-900",children:k.id}),l.jsx("td",{className:"py-3 px-4 text-sm text-gray-900 font-medium",children:k.name}),l.jsx("td",{className:"py-3 px-4 text-sm text-gray-500",children:k.description})]},k.id))})]})})]})]}),e==="parents"&&l.jsxs("div",{children:[l.jsxs("div",{className:"card mb-6 bg-white rounded-lg shadow-lg p-6 border-t-4 border-teal-500",children:[l.jsx("h2",{className:"text-2xl font-bold mb-4 text-teal-700",children:"Register New Parent"}),i&&l.jsx("div",{className:`p-4 mb-4 rounded-md ${i.includes("successfully")?"bg-green-100 text-green-700":"bg-red-100 text-red-700"}`,role:"alert",children:i}),l.jsxs("form",{onSubmit:he,children:[l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"username",className:"form-label text-gray-700 font-medium",children:"Username"}),l.jsx("input",{type:"text",className:"form-input w-full rounded-md border-gray-300 shadow-sm focus:border-teal-500 focus:ring focus:ring-teal-200 focus:ring-opacity-50",id:"username",name:"username",value:S.username,onChange:oe,required:!0})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"password",className:"form-label text-gray-700 font-medium",children:"Password"}),l.jsx("input",{type:"password",className:"form-input w-full rounded-md border-gray-300 shadow-sm focus:border-teal-500 focus:ring focus:ring-teal-200 focus:ring-opacity-50",id:"password",name:"password",value:S.password,onChange:oe,required:!0})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"email",className:"form-label text-gray-700 font-medium",children:"Email"}),l.jsx("input",{type:"email",className:"form-input w-full rounded-md border-gray-300 shadow-sm focus:border-teal-500 focus:ring focus:ring-teal-200 focus:ring-opacity-50",id:"email",name:"email",value:S.email,onChange:oe,required:!0})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"first_name",className:"form-label text-gray-700 font-medium",children:"First Name"}),l.jsx("input",{type:"text",className:"form-input w-full rounded-md border-gray-300 shadow-sm focus:border-teal-500 focus:ring focus:ring-teal-200 focus:ring-opacity-50",id:"first_name",name:"first_name",value:N.first_name,onChange:k=>w({...N,first_name:k.target.value}),required:!0})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"last_name",className:"form-label text-gray-700 font-medium",children:"Last Name"}),l.jsx("input",{type:"text",className:"form-input w-full rounded-md border-gray-300 shadow-sm focus:border-teal-500 focus:ring focus:ring-teal-200 focus:ring-opacity-50",id:"last_name",name:"last_name",value:N.last_name,onChange:k=>w({...N,last_name:k.target.value}),required:!0})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"occupation",className:"form-label text-gray-700 font-medium",children:"Occupation"}),l.jsx("input",{type:"text",className:"form-input w-full rounded-md border-gray-300 shadow-sm focus:border-teal-500 focus:ring focus:ring-teal-200 focus:ring-opacity-50",id:"occupation",name:"occupation",value:N.occupation,onChange:k=>w({...N,occupation:k.target.value})})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"address",className:"form-label text-gray-700 font-medium",children:"Address"}),l.jsx("input",{type:"text",className:"form-input w-full rounded-md border-gray-300 shadow-sm focus:border-teal-500 focus:ring focus:ring-teal-200 focus:ring-opacity-50",id:"address",name:"address",value:N.address,onChange:k=>w({...N,address:k.target.value})})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"phone",className:"form-label text-gray-700 font-medium",children:"Phone"}),l.jsx("input",{type:"text",className:"form-input w-full rounded-md border-gray-300 shadow-sm focus:border-teal-500 focus:ring focus:ring-teal-200 focus:ring-opacity-50",id:"phone",name:"phone",value:N.phone,onChange:k=>w({...N,phone:k.target.value})})]})]}),l.jsx("button",{type:"submit",className:"mt-4 px-6 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-opacity-50 transition-colors duration-300",disabled:s,children:s?"Registering...":"Register Parent"})]})]}),l.jsxs("div",{className:"card bg-white rounded-lg shadow-lg p-6",children:[l.jsx("h2",{className:"text-2xl font-bold mb-4 text-teal-700",children:"All Parents"}),x?l.jsx("p",{className:"text-gray-600",children:"Loading parents..."}):l.jsx("div",{className:"overflow-x-auto",children:l.jsxs("table",{className:"min-w-full bg-white border border-gray-200 rounded-lg overflow-hidden",children:[l.jsx("thead",{className:"bg-gray-50",children:l.jsxs("tr",{children:[l.jsx("th",{className:"py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"ID"}),l.jsx("th",{className:"py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User ID"}),l.jsx("th",{className:"py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"First Name"}),l.jsx("th",{className:"py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Last Name"}),l.jsx("th",{className:"py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Phone"})]})}),l.jsx("tbody",{className:"divide-y divide-gray-200",children:g.map(k=>l.jsxs("tr",{className:"hover:bg-gray-50",children:[l.jsx("td",{className:"py-3 px-4 text-sm text-gray-900",children:k.id}),l.jsx("td",{className:"py-3 px-4 text-sm text-gray-900",children:k.user_id}),l.jsx("td",{className:"py-3 px-4 text-sm text-gray-900 font-medium",children:k.first_name}),l.jsx("td",{className:"py-3 px-4 text-sm text-gray-900 font-medium",children:k.last_name}),l.jsx("td",{className:"py-3 px-4 text-sm text-gray-500",children:k.phone})]},k.id))})]})})]})]}),e==="parentMapping"&&l.jsx("div",{children:l.jsxs("div",{className:"card mb-6 bg-white rounded-lg shadow-lg p-6 border-t-4 border-amber-500",children:[l.jsx("h2",{className:"text-2xl font-bold mb-4 text-amber-700",children:"Map Parent to Student"}),i&&l.jsx("div",{className:`p-4 mb-4 rounded-md ${i.includes("successfully")?"bg-green-100 text-green-700":i.includes("Note:")?"bg-blue-100 text-blue-700":"bg-red-100 text-red-700"}`,role:"alert",children:i}),l.jsxs("form",{onSubmit:Te,children:[l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"student_id",className:"form-label text-gray-700 font-medium",children:"Student"}),l.jsxs("select",{className:"form-input w-full rounded-md border-gray-300 shadow-sm focus:border-amber-500 focus:ring focus:ring-amber-200 focus:ring-opacity-50",id:"student_id",name:"student_id",value:F.student_id,onChange:V,required:!0,children:[l.jsx("option",{value:"",children:"Select Student"}),r.map(k=>l.jsxs("option",{value:k.id,children:[k.first_name," ",k.last_name]},k.id))]})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"parent_id",className:"form-label text-gray-700 font-medium",children:"Parent"}),l.jsxs("select",{className:"form-input w-full rounded-md border-gray-300 shadow-sm focus:border-amber-500 focus:ring focus:ring-amber-200 focus:ring-opacity-50",id:"parent_id",name:"parent_id",value:F.parent_id,onChange:V,required:!0,children:[l.jsx("option",{value:"",children:"Select Parent"}),g.map(k=>l.jsxs("option",{value:k.id,children:[k.first_name," ",k.last_name]},k.id))]})]}),l.jsxs("div",{className:"form-group",children:[l.jsx("label",{htmlFor:"relationship",className:"form-label text-gray-700 font-medium",children:"Relationship"}),l.jsxs("select",{className:"form-input w-full rounded-md border-gray-300 shadow-sm focus:border-amber-500 focus:ring focus:ring-amber-200 focus:ring-opacity-50",id:"relationship",name:"relationship",value:F.relationship,onChange:V,required:!0,children:[l.jsx("option",{value:"",children:"Select Relationship"}),l.jsx("option",{value:"Father",children:"Father"}),l.jsx("option",{value:"Mother",children:"Mother"}),l.jsx("option",{value:"Guardian",children:"Guardian"})]})]})]}),Se.length>0&&l.jsxs("div",{className:"mt-4 p-4 bg-blue-50 rounded-md border border-blue-200",children:[l.jsx("h3",{className:"text-lg font-semibold text-blue-700 mb-2",children:"Existing Parents for Selected Student"}),l.jsx("ul",{className:"list-disc pl-5 space-y-1",children:Se.map(k=>l.jsxs("li",{className:"text-blue-600",children:[k.first_name," ",k.last_name," - ",l.jsx("span",{className:"font-medium",children:k.relationship})]},k.id))})]}),l.jsx("button",{type:"submit",className:"mt-4 px-6 py-2 bg-amber-600 text-white rounded-md hover:bg-amber-700 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-opacity-50 transition-colors duration-300",disabled:s,children:s?"Mapping...":"Map Parent to Student"})]})]})})]})},Sx=()=>{const{currentUser:e}=lr(),[t,r]=j.useState(null),[n,s]=j.useState(!1),[o,i]=j.useState(""),[a,u]=j.useState([]),[c,f]=j.useState(!1),d=async()=>{try{s(!0),i("");try{const y=await Pe.getStudentProfile();if(y&&y.student){r(y.student),await g(y.student.id);return}}catch(y){console.warn("Error getting student profile, falling back to getStudents",y)}const x=((await Pe.getStudents()).students||[]).find(y=>y.user_id===parseInt(e.id));x?(r(x),await g(x.id)):i("Student profile not found")}catch(b){i(b.error||"Failed to load student data")}finally{s(!1)}},g=async b=>{try{f(!0);const x=await ct.getStudentCourses(b);u(x.courses||[])}catch(x){console.error("Failed to load courses:",x)}finally{f(!1)}};return j.useEffect(()=>{d()},[]),l.jsx(os,{title:"Student Dashboard",children:n?l.jsx("div",{className:"flex justify-center items-center h-64",children:l.jsx("p",{className:"text-lg",children:"Loading student data..."})}):o?l.jsx("div",{className:"alert alert-danger",role:"alert",children:o}):t?l.jsxs("div",{children:[l.jsxs("div",{className:"card mb-6",children:[l.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Student Profile"}),l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[l.jsxs("div",{children:[l.jsxs("p",{className:"mb-2",children:[l.jsx("strong",{children:"Name:"})," ",t.first_name," ",t.last_name]}),l.jsxs("p",{className:"mb-2",children:[l.jsx("strong",{children:"Date of Birth:"})," ",t.date_of_birth||"Not specified"]})]}),l.jsxs("div",{children:[l.jsxs("p",{className:"mb-2",children:[l.jsx("strong",{children:"Address:"})," ",t.address||"Not specified"]}),l.jsxs("p",{className:"mb-2",children:[l.jsx("strong",{children:"Phone:"})," ",t.phone||"Not specified"]})]})]})]}),l.jsxs("div",{className:"card",children:[l.jsx("h2",{className:"text-xl font-semibold mb-4",children:"My Courses"}),c?l.jsx("p",{children:"Loading courses..."}):a.length===0?l.jsx("p",{children:"You are not enrolled in any courses yet."}):l.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:a.map(b=>l.jsxs("div",{className:"bg-white p-4 rounded shadow",children:[l.jsx("h3",{className:"text-lg font-semibold",children:b.course.name}),l.jsx("p",{className:"text-gray-600",children:b.course.description||"No description"})]},b.id))})]})]}):l.jsx("div",{className:"alert alert-danger",role:"alert",children:"No student data available."})})},Cx=()=>{const{currentUser:e}=lr(),[t,r]=j.useState(null),[n,s]=j.useState(!1),[o,i]=j.useState(""),[a,u]=j.useState([]),[c,f]=j.useState(!1),[d,g]=j.useState(null),[b,x]=j.useState([]),[y,S]=j.useState(!1),m=async()=>{try{s(!0),i("");const w=await jr.getParentByUserId(e.id);r(w.parent),w.parent&&await p(w.parent.id)}catch(w){i(w.error||"Failed to load parent data")}finally{s(!1)}},p=async w=>{try{f(!0);const _=await Pe.getParentStudents(w);u(_.students||[])}catch(_){console.error("Failed to load children:",_)}finally{f(!1)}},h=async w=>{try{S(!0);const _=await ct.getStudentCourses(w);x(_.courses||[])}catch(_){console.error("Failed to load courses:",_)}finally{S(!1)}},N=w=>{g(w.student),h(w.student_id)};return j.useEffect(()=>{m()},[]),l.jsx(os,{title:"Parent Dashboard",children:n?l.jsx("div",{className:"flex justify-center items-center h-64",children:l.jsx("p",{className:"text-lg",children:"Loading parent data..."})}):o?l.jsx("div",{className:"alert alert-danger",role:"alert",children:o}):t?l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[l.jsxs("div",{className:"md:col-span-1",children:[l.jsxs("div",{className:"card mb-6",children:[l.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Parent Profile"}),l.jsxs("div",{children:[l.jsxs("p",{className:"mb-2",children:[l.jsx("strong",{children:"Name:"})," ",t.first_name," ",t.last_name]}),l.jsxs("p",{className:"mb-2",children:[l.jsx("strong",{children:"Occupation:"})," ",t.occupation||"Not specified"]}),l.jsxs("p",{className:"mb-2",children:[l.jsx("strong",{children:"Address:"})," ",t.address||"Not specified"]}),l.jsxs("p",{className:"mb-2",children:[l.jsx("strong",{children:"Phone:"})," ",t.phone||"Not specified"]})]})]}),l.jsxs("div",{className:"card",children:[l.jsx("h2",{className:"text-xl font-semibold mb-4",children:"My Children"}),c?l.jsx("p",{children:"Loading children..."}):a.length===0?l.jsx("p",{children:"No children found."}):l.jsx("div",{className:"space-y-2",children:a.map(w=>l.jsxs("div",{className:`p-3 rounded cursor-pointer ${d&&d.id===w.student.id?"bg-blue-100 border-blue-500 border":"bg-gray-100 hover:bg-gray-200"}`,onClick:()=>N(w),children:[l.jsxs("p",{className:"font-semibold",children:[w.student.first_name," ",w.student.last_name]}),l.jsxs("p",{className:"text-sm text-gray-600",children:["Relationship: ",w.relationship||"Not specified"]})]},w.id))})]})]}),l.jsx("div",{className:"md:col-span-2",children:d?l.jsxs("div",{children:[l.jsxs("div",{className:"card mb-6",children:[l.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Child Details"}),l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[l.jsxs("div",{children:[l.jsxs("p",{className:"mb-2",children:[l.jsx("strong",{children:"Name:"})," ",d.first_name," ",d.last_name]}),l.jsxs("p",{className:"mb-2",children:[l.jsx("strong",{children:"Date of Birth:"})," ",d.date_of_birth||"Not specified"]})]}),l.jsxs("div",{children:[l.jsxs("p",{className:"mb-2",children:[l.jsx("strong",{children:"Address:"})," ",d.address||"Not specified"]}),l.jsxs("p",{className:"mb-2",children:[l.jsx("strong",{children:"Phone:"})," ",d.phone||"Not specified"]})]})]})]}),l.jsxs("div",{className:"card",children:[l.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Child's Courses"}),y?l.jsx("p",{children:"Loading courses..."}):b.length===0?l.jsx("p",{children:"Your child is not enrolled in any courses yet."}):l.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:b.map(w=>l.jsxs("div",{className:"bg-white p-4 rounded shadow",children:[l.jsx("h3",{className:"text-lg font-semibold",children:w.course.name}),l.jsx("p",{className:"text-gray-600",children:w.course.description||"No description"})]},w.id))})]})]}):l.jsx("div",{className:"card flex items-center justify-center h-64",children:l.jsx("p",{className:"text-gray-500",children:"Select a child to view details"})})})]}):l.jsx("div",{className:"alert alert-danger",role:"alert",children:"No parent data available."})})},kx=()=>{const{currentUser:e}=lr(),t=ns();if(j.useEffect(()=>{e||t("/login")},[e,t]),!e)return null;switch(e.role){case"Super Admin":return l.jsx(Nx,{});case"Admin":return l.jsx(jx,{});case"Teacher":return l.jsx(bx,{});case"Student":return l.jsx(Sx,{});case"Parent":return l.jsx(Cx,{});default:return l.jsx("div",{className:"container mx-auto p-4",children:l.jsxs("div",{className:"card",children:[l.jsx("h1",{className:"text-2xl font-bold mb-4",children:"Unknown Role"}),l.jsxs("p",{children:["Your role (",e.role,") is not recognized."]})]})})}};function _x(){return l.jsx(hx,{children:l.jsxs(w0,{children:[l.jsx(Us,{path:"/login",element:l.jsx(yx,{})}),l.jsx(Us,{path:"/dashboard/*",element:l.jsx(gx,{children:l.jsx(kx,{})})}),l.jsx(Us,{path:"*",element:l.jsx(hi,{to:"/dashboard"})})]})})}Y.defaults.withCredentials=!0;Y.defaults.headers.common["Content-Type"]="application/json";Y.defaults.headers.common.Accept="application/json";Y.interceptors.request.use(e=>e,e=>(console.error("Axios request error:",e),Promise.reject(e)));Y.interceptors.response.use(e=>e,e=>(e.response?console.error("Response error:",e.response.status,e.response.data):e.request?console.error("Request error:",e.request):console.error("Error:",e.message),Promise.reject(e)));vf(document.getElementById("root")).render(l.jsx(j.StrictMode,{children:l.jsx(_0,{children:l.jsx(_x,{})})}));
